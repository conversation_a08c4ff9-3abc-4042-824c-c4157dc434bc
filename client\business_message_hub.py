"""
业务消息处理中心 - 运行在子线程中
负责处理业务逻辑并实现与主线程的双向信号通信
"""

import asyncio
from typing import Dict, Callable

class BusinessMessageHub:
    """业务消息处理中心 - 运行在子线程中"""

    def __init__(self, loop, logger, business_signal, ui_signal):
        self.loop = loop
        self.logger = logger
        self.business_signal = business_signal  # 向主线程发送信号
        self.ui_signal = ui_signal              # 接收主线程信号
        self.business_network = None            # 网络通信模块引用，由BusinessThreadedManager设置
        self.is_running = True

        # 绑定主线程UI信号到自身的槽函数
        self.ui_signal.connect(self.submit_ui_message)

        # 注册消息处理器
        self.message_handlers: Dict[str, Callable] = {
            'user_register': self.send_message_to_server,
            'user_login': self.send_message_to_server,
            'user_register_response': self.send_message_to_main,
            'user_login_response': self.send_message_to_main,
            'room_event': self.send_message_to_server,
        }
        self.logger.info("BusinessMessageHub 创建完成")

    def set_business_network(self, business_network):
        """设置网络通信模块引用"""
        self.business_network = business_network
        self.logger.info("BusinessNetwork 引用已设置")

    # 主线程信号进行跨线程移交方法(槽函数只能是同步函数，并且此线程无QT事件循环，此函数由QT线程直接调用！)
    def submit_ui_message(self, message: dict):
        """提交UI消息到事件循环"""
        if self.loop is not None and self.is_running:
            asyncio.run_coroutine_threadsafe(self.handle_message(message), self.loop)

    # ==================== 统一的异步消息处理入口 ====================

    async def handle_message(self, message: dict):
        """统一的UI消息处理器 - 在asyncio事件循环中接收主线程消息"""
        message_type = message.get("type", "unknown")
        data = message.get("data", {})

        # 根据消息类型分发到具体处理方法
        if data:
            await self.message_handlers.get(message_type, self._handle_default_message)(message)
        else:
            self.logger.warning(f"收到空数据的消息: {message_type}")

    '''================ 未知类型消息收集器 ================'''

    async def _handle_default_message(self, message: dict):
        """未知类型的消息过滤到这里处理"""
        message_type = message.get('type', 'unknown')
        await asyncio.sleep(0.1)
        self.logger.warning(f"未找到消息类型 '{message_type}' 的处理器")


    # ==================== 公共接口方法 ====================

    # 向UI主线程发送消息的接口
    async def send_message_to_main(self, message):
        """向UI主线程发送消息"""
        message_type = message.get("type", "unknown")
        self.logger.info(f"BusinessMessageHub 发送消息到主线程: {message_type}")
        self.business_signal.emit(message)

    # 向服务器发送消息的接口
    async def send_message_to_server(self, message):
        """向服务器发送消息"""
        if self.business_network is None:
            self.logger.error("❌ 无法发送消息到服务器：business_network 未设置")
            return False

        message_type = message.get("type", "unknown")
        self.logger.info(f"BusinessMessageHub 发送消息到服务器: {message_type}")
        return await self.business_network.send_message(message)
    