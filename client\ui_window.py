from PySide6.QtWidgets import QMainWindow, QApplication, QFrame, QLabel, QGridLayout, QVBoxLayout, QHBoxLayout, QPushButton, QGraphicsView, QGraphicsScene, QMessageBox, QWidget, QDialog, QLineEdit, QFormLayout, QTabWidget, QTextEdit, QStackedWidget, QStackedWidget, QScrollArea
from PySide6.QtGui import QIcon, QPainter, QPixmap, QBrush, QFont, QPixmap, QRegularExpressionValidator, QKeySequence, QPen, QColor, QPainterPath, QPolygonF
from PySide6.QtCore import Qt, QRegularExpression, QEvent, QPointF, QRect, QTimer
from PySide6.QtSvgWidgets import QGraphicsSvgItem
import os, hashlib


# 自定义提示框widget
class CustomTooltip(QWidget):
    """自定义提示框widget（单例模式）"""

    _instance = None

    def __new__(cls, parent=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, parent=None):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 创建标签显示文本
        self.label = QLabel()
        self.label.setWordWrap(True)

        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.label)

        # 标记已初始化
        self._initialized = True

    def show_tooltip(self, text, position, is_playing=False):
        """显示提示框"""
        # 根据房间状态设置主题颜色
        if is_playing:
            # 游戏中房间：橙色主题
            style = """
                QLabel {
                    background-color: #4a3728;
                    color: #ffcc99;
                    border: 2px solid #b77f54;
                    border-radius: 3px;
                    padding: 8px;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """
        else:
            # 准备中房间：青色主题
            style = """
                QLabel {
                    background-color: #1a4a47;
                    color: #99ffff;
                    border: 2px solid #2fd5c9;
                    border-radius: 3px;
                    padding: 8px;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """
        self.label.setStyleSheet(style)
        self.label.setText(text)
        self.adjustSize()  # 自动调整大小
        self.move(position)
        self.show()
        self.raise_()  # 确保在最上层

    def hide_tooltip(self):
        """隐藏提示框"""
        self.hide()

#房间项widget
class RoomItemWidget(QWidget):
    """自定义房间项widget，支持QPainter背景绘制"""

    def __init__(self, logger, room_name, status, mainwindow, parent=None):
        super().__init__(parent)
        self.logger = logger
        self.room_name = room_name
        self.mainwindow = mainwindow
        self.is_playing = (status == 1)
        self.is_hovered = False  # 添加hover状态变量

        # 房间成员列表（仅维护，不用于显示）
        self.room_members = []

        # 座位锁定信息（用于头像绘制和人数显示）
        self.seat_locks = {}  # {seat_index: {"user_id": "123", "user_name": "Alice"}}

        # 房间级头像缓存（优先查找）
        self.room_avatar_cache = {}  # {user_id: QPixmap}

        # 默认头像路径
        self.default_avatar_path = "./client/image/user1.png"  # 使用 user1.png 作为默认

        # 初始化玩家显示信息
        self.players = "0/4 已锁定"  # 默认显示

        # 加载背景图片（根据房间状态选择不同背景）
        self.background_pixmap = None
        if self.is_playing:
            # 正在游戏的房间使用room1.png
            self.background_pixmap = QPixmap("./client/image/room1.png")
            if self.background_pixmap.isNull():
                print("警告: 无法加载房间背景图片 room1.png")
        else:
            # 准备中的房间使用room2.png
            self.background_pixmap = QPixmap("./client/image/room2.png")
            if self.background_pixmap.isNull():
                print("警告: 无法加载房间背景图片 room2.png")

        # 所有房间都使用统一的座位锁定系统

        self.setFixedHeight(60)
        self.setup_ui()

        # 安装事件过滤器
        self.installEventFilter(self)

    def setup_ui(self):
        """设置UI布局"""
        # 设置基础样式（移除CSS hover，改用事件过滤器）
        base_style = """
            RoomItemWidget {
                border: 0px solid #6a6a6a;
                border-radius: 3px;
                margin: 2px;
            }
            QLabel {
                background-color: transparent;
                border: none;
            }
        """

        # 根据房间状态设置不同的背景色（现在所有房间都有背景图片）
        if self.is_playing:
            # 正在游戏的房间 - 透明背景，让QPainter绘制的room1.png显示
            background_color = "rgba(90, 90, 90, 80)"  # 半透明，让背景图片透出
        else:
            # 准备中的房间 - 透明背景，让QPainter绘制的room2.png显示
            background_color = "rgba(90, 90, 90, 80)"  # 半透明，让背景图片透出

        self.setStyleSheet(base_style + f"""
            RoomItemWidget {{
                background-color: {background_color};
            }}
        """)

        # 创建布局
        main_layout = QHBoxLayout(self)

        # 为头像预留空间（统一设置）
        avatar_space = 185  # 固定预留空间，足够容纳4个头像
        main_layout.setContentsMargins(avatar_space, 8, 10, 8)
        main_layout.setSpacing(0)

        # 添加弹性空间，将文字推到右侧
        main_layout.addStretch()

        # 文字信息区域
        text_layout = QVBoxLayout()
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)

        # 房间名称
        self.name_label = QLabel(self.room_name)
        name_color = "#ffffff" if not self.is_playing else "#ffff00"  # 正在游戏的房间用黄色
        self.name_label.setStyleSheet(f"font-size: 12px; font-weight: bold; color: {name_color};")
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.name_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        # 玩家信息
        self.players_label = QLabel(self.players)
        players_color = "#cccccc" if not self.is_playing else "#ffcccc"  # 正在游戏的房间用浅红色
        self.players_label.setStyleSheet(f"font-size: 10px; color: {players_color};")
        self.players_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.players_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        # 状态信息
        self.status_label = QLabel('正在游戏' if self.is_playing else '准备游戏')
        status_color = "#ff6b6b" if self.is_playing else "#2fd5c9"
        self.status_label.setStyleSheet(f"font-size: 10px; color: {status_color}; font-weight: bold;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.status_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        text_layout.addWidget(self.status_label)
        text_layout.addWidget(self.players_label)
        text_layout.addWidget(self.name_label)

        main_layout.addLayout(text_layout)

    def eventFilter(self, obj, event):
        """事件过滤器，处理hover效果和工具提示"""
        if obj == self:
            if event.type() == QEvent.Type.Enter:
                self.is_hovered = True
                self._show_room_tooltip()
                self.update()  # 触发重绘
                return True
            elif event.type() == QEvent.Type.Leave:
                self.is_hovered = False
                CustomTooltip().hide_tooltip()
                self.update()  # 触发重绘
                return True
            elif event.type() == QEvent.Type.MouseButtonPress and event.button() == Qt.MouseButton.LeftButton:
                if not self.is_playing and not self.mainwindow.I_am_playing:
                    self.logger.info(f'点击了房间: {self.room_name}')
                    self.mainwindow.message_hub.send_to_business_thread({
                        'type': 'room_event',
                        'data': {
                            'type': 'enter_room',
                            'room_name': self.room_name
                        }
                    })
                    return True
        return super().eventFilter(obj, event)

    def draw_star(self, painter, center_x, center_y, radius, color):
        """绘制五角星"""
        import math

        # 五角星的5个外顶点和5个内顶点
        points = []
        for i in range(10):
            angle = i * math.pi / 5 - math.pi / 2  # 从顶部开始
            if i % 2 == 0:
                # 外顶点
                r = radius
            else:
                # 内顶点
                r = radius * 0.4

            x = center_x + r * math.cos(angle)
            y = center_y + r * math.sin(angle)
            points.append(QPointF(x, y))

        # 创建五角星路径
        star_polygon = QPolygonF(points)

        # 绘制五角星
        painter.save()
        painter.setBrush(color)
        painter.setPen(QPen(color, 1))
        painter.drawPolygon(star_polygon)
        painter.restore()

    def _show_room_tooltip(self):
        """显示房间信息工具提示"""
        if not self.seat_locks:
            # 空房间显示基本信息
            tooltip_text = f"房间: {self.room_name}\n状态: {'正在游戏' if self.is_playing else '准备游戏'}\n座位: 0/4 已锁定"
        else:
            # 有锁定座位的房间显示详细信息
            tooltip_lines = [f"房间: {self.room_name}", f"状态: {'正在游戏' if self.is_playing else '准备游戏'}", ""]

            # 按座位索引排序显示
            for seat_index in sorted(self.seat_locks.keys()):
                user_info = self.seat_locks[seat_index]
                user_name = user_info.get("user_name", "未知用户")
                # 座位号从1开始显示（用户习惯）
                tooltip_lines.append(f"座位{seat_index + 1}: {user_name}")

            tooltip_text = "\n".join(tooltip_lines)

        # 显示工具提示在房间右侧，考虑滚动区域边界
        room_rect = self.rect()
        # 计算房间右侧位置：房间右边缘 + 10px间距
        tooltip_x = room_rect.right() + 10
        # 初始Y位置：与房间顶部对齐
        tooltip_y = room_rect.top()

        # 转换为全局坐标
        local_point = QPointF(tooltip_x, tooltip_y)
        global_pos = self.mapToGlobal(local_point.toPoint())

        # 查找父级滚动区域并调整位置
        scroll_area = self._find_scroll_area()
        if scroll_area:
            # 获取滚动区域viewport的全局边界（这是实际可见区域）
            viewport = scroll_area.viewport()
            viewport_rect = viewport.rect()
            viewport_global_pos = viewport.mapToGlobal(viewport_rect.topLeft())
            scroll_top = viewport_global_pos.y()
            scroll_bottom = viewport_global_pos.y() + viewport_rect.height()

            # 预估提示框高度（临时创建来测量）
            temp_tooltip = CustomTooltip()
            temp_tooltip.label.setText(tooltip_text)
            temp_tooltip.adjustSize()
            tooltip_height = temp_tooltip.height()

            # 调整Y坐标以适应滚动区域边界
            # 如果房间顶部超过滚动区域顶部，以滚动区域顶部为准
            if global_pos.y() < scroll_top:
                global_pos.setY(scroll_top)

            # 如果提示框底部超过滚动区域底部，向上调整
            if global_pos.y() + tooltip_height > scroll_bottom:
                global_pos.setY(scroll_bottom - tooltip_height)

        CustomTooltip().show_tooltip(tooltip_text, global_pos, self.is_playing)

    def _find_scroll_area(self):
        """查找父级滚动区域"""
        parent = self.parent()
        while parent:
            if isinstance(parent, QScrollArea):
                return parent
            parent = parent.parent()
        return None

    def paintEvent(self, _):
        """重写绘制事件，手动处理hover效果和背景绘制"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景色
        if self.is_hovered:
            # hover状态的背景
            painter.fillRect(self.rect(), QColor(106, 106, 106, 150))
        else:
            # 正常状态的背景
            if self.is_playing:
                painter.fillRect(self.rect(), QColor(90, 90, 90, 80))
            else:
                painter.fillRect(self.rect(), QColor(90, 90, 90, 80))

        # 绘制背景图片
        if self.background_pixmap and not self.background_pixmap.isNull():
            # 缩放背景图片以适应widget大小
            scaled_bg = self.background_pixmap.scaled(
                self.width(), self.height(),
                Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                Qt.TransformationMode.SmoothTransformation
            )

            # 计算绘制位置（居中）
            bg_x = (self.width() - scaled_bg.width()) // 2
            bg_y = (self.height() - scaled_bg.height()) // 2

            # 根据房间状态设置不同的透明度
            if self.is_playing:
                painter.setOpacity(0.5)  # 正在游戏的房间：40%透明度，更明显
            else:
                painter.setOpacity(0.25)  # 准备中的房间：25%透明度，更柔和

            painter.drawPixmap(bg_x, bg_y, scaled_bg)
            painter.setOpacity(1.0)  # 恢复完全不透明

        # 绘制头像（统一的绘制逻辑）
        self._draw_room_avatars(painter)

        # 绘制2px深灰色外框（所有房间）
        painter.save()
        pen = QPen(QColor(80, 80, 80), 2)  # 更深的灰色，2px粗
        painter.setPen(pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 3, 3)
        painter.restore()

        # 绘制hover边框（根据房间状态选择颜色）
        if self.is_hovered:
            if self.is_playing:
                hover_color = QColor(183, 127, 84)  # 游戏中房间：橙色
            else:
                hover_color = QColor(47, 213, 201)  # 准备中房间：青色

            pen = QPen(hover_color, 3)  # 3px粗
            painter.setPen(pen)
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 3, 3)  # 内缩避免与外框重叠


    # 房间成员管理（仅维护，不用于显示）
    def add_room_member(self, player_name):
        """添加房间成员（仅维护列表）"""
        if player_name not in self.room_members:
            self.room_members.append(player_name)

    def remove_room_member(self, player_name):
        """移除房间成员（仅维护列表）"""
        if player_name in self.room_members:
            self.room_members.remove(player_name)

    # 座位锁定管理（用于头像绘制和人数显示）
    def lock_seat(self, seat_index, user_info):
        """锁定座位（服务器确认后调用）"""
        if 0 <= seat_index <= 3:
            self.seat_locks[seat_index] = user_info
            self._load_user_avatar(user_info)
            self._update_locked_seats_display()
            self.update()  # 触发重绘

    def unlock_seat(self, seat_index):
        """解锁座位"""
        if seat_index in self.seat_locks:
            self.seat_locks.pop(seat_index)
            self._update_locked_seats_display()
            self.update()  # 触发重绘

    def _update_locked_seats_display(self):
        """更新锁定座位数量显示"""
        locked_count = len(self.seat_locks)

        if self.is_playing:
            # 游戏中房间：显示玩家名称
            if locked_count <= 2:
                # 少于等于2人时显示所有名称
                names = []
                for seat_index in sorted(self.seat_locks.keys()):
                    user_info = self.seat_locks[seat_index]
                    names.append(user_info.get("user_name", ""))
                self.players = ", ".join(names)
            else:
                # 超过2人时显示第一个名称+人数
                first_user = None
                for seat_index in sorted(self.seat_locks.keys()):
                    first_user = self.seat_locks[seat_index]
                    break
                if first_user:
                    first_name = first_user.get("user_name", "")
                    self.players = f"{first_name}等{locked_count}人"
                else:
                    self.players = f"{locked_count}/4 已锁定"
        else:
            # 准备中房间：显示锁定座位数/4
            self.players = f"{locked_count}/4 已锁定"

        self.players_label.setText(self.players)

    def _load_user_avatar(self, user_info):
        """加载用户头像（三级查找策略）"""
        user_id = user_info.get("user_id")
        if not user_id:
            return

        # 1. 先在房间头像缓存中查找
        if user_id in self.room_avatar_cache:
            return

        # 2. 在统一本地缓存文件夹中查找
        import os
        local_cache_path = f"./client/image/cache/avatars/{user_id}.png"
        if os.path.exists(local_cache_path):
            pixmap = QPixmap(local_cache_path)
            if not pixmap.isNull():
                self.room_avatar_cache[user_id] = pixmap
                return

        # 3. 使用默认头像
        default_pixmap = QPixmap(self.default_avatar_path)
        if not default_pixmap.isNull():
            self.room_avatar_cache[user_id] = default_pixmap
        else:
            # 最后的备用方案
            backup_pixmap = QPixmap("./client/image/user1.png")
            self.room_avatar_cache[user_id] = backup_pixmap

    def clear_avatar_cache(self):
        """清理房间头像缓存（游戏开始时调用）"""
        self.room_avatar_cache.clear()

    def get_locked_seats_count(self):
        """获取已锁定座位数量"""
        return len(self.seat_locks)

    def get_room_members(self):
        """获取房间成员列表"""
        return self.room_members.copy()

    def is_full(self):
        """检查房间是否已满"""
        return len(self.seat_locks) >= 4

    def is_empty(self):
        """检查房间是否为空"""
        return len(self.seat_locks) == 0

    def _draw_room_avatars(self, painter):
        """统一的房间头像绘制方法"""
        if not self.seat_locks:
            # 没有锁定座位时只绘制五角星
            self._draw_star_only(painter)
            return

        # 头像大小等于房间高度
        avatar_size = self.height()

        # 计算15度倾斜的偏移量
        import math
        angle_rad = math.radians(15)  # 15度转弧度
        skew_offset = avatar_size * math.tan(angle_rad)  # 倾斜偏移量

        # 统一的平行四边形下底长度
        uniform_bottom_length = 40  # 固定下底长40px
        left_offset = 25  # 整体向右偏移25px

        # 根据房间状态确定颜色
        if self.is_playing:
            line_color = QColor(160, 108, 68)  # 橙色
            star_color = QColor(184, 127, 84)  # 橙色
        else:
            line_color = QColor(47, 213, 201)  # 青色
            star_color = QColor(46, 213, 200)  # 青色

        # 绘制头像（按实际座位位置）
        for seat_index, user_info in self.seat_locks.items():
            user_id = user_info.get("user_id")
            if user_id in self.room_avatar_cache:
                avatar = self.room_avatar_cache[user_id]

                # 等比例缩放头像
                scale_width = int(uniform_bottom_length + skew_offset)  # 40 + 16.08 ≈ 56px
                scaled_avatar = avatar.scaled(
                    scale_width, avatar_size,
                    Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                    Qt.TransformationMode.SmoothTransformation
                )

                # 居中裁剪到需要的尺寸
                if scaled_avatar.width() > scale_width:
                    crop_x = (scaled_avatar.width() - scale_width) // 2
                    cropped_avatar = scaled_avatar.copy(crop_x, 0, scale_width, avatar_size)
                elif scaled_avatar.height() > avatar_size:
                    crop_y = (scaled_avatar.height() - avatar_size) // 2
                    cropped_avatar = scaled_avatar.copy(0, crop_y, scale_width, avatar_size)
                else:
                    cropped_avatar = scaled_avatar

                # 计算头像位置 - 按实际座位索引排列
                avatar_x = left_offset + seat_index * uniform_bottom_length

                # 创建向右倾斜15度的平行四边形裁剪路径
                clip_path = QPainterPath()
                points = [
                    QPointF(avatar_x, avatar_size),                                    # 左下角
                    QPointF(avatar_x + skew_offset, 0),                               # 左上角（向右偏移）
                    QPointF(avatar_x + uniform_bottom_length + skew_offset, 0),       # 右上角
                    QPointF(avatar_x + uniform_bottom_length, avatar_size)            # 右下角
                ]

                # 创建多边形路径
                polygon = QPolygonF(points)
                clip_path.addPolygon(polygon)

                # 保存当前绘制状态
                painter.save()

                # 设置裁剪路径
                painter.setClipPath(clip_path)

                # 绘制头像
                painter.drawPixmap(avatar_x, 0, cropped_avatar)

                # 恢复绘制状态
                painter.restore()

                # 绘制分隔边线（左右边线）
                painter.save()
                pen = QPen(line_color, 4)  # 根据房间状态选择颜色，4px粗
                painter.setPen(pen)

                # 绘制左边线（从左下角到左上角）
                painter.drawLine(
                    QPointF(avatar_x, avatar_size),                    # 左下角
                    QPointF(avatar_x + skew_offset, 0)                 # 左上角
                )

                # 绘制右边线（从右下角到右上角）
                painter.drawLine(
                    QPointF(avatar_x + uniform_bottom_length, avatar_size),          # 右下角
                    QPointF(avatar_x + uniform_bottom_length + skew_offset, 0)       # 右上角
                )

                painter.restore()

        # 绘制五角星
        star_radius = int(15 * 0.5)  # 缩小50%，半径约7.5px
        self.draw_star(painter, 15, 15, star_radius, star_color)



    def _draw_star_only(self, painter):
        """只绘制五角星（用于空房间）"""
        # 根据房间状态确定颜色
        star_color = QColor(184, 127, 84) if self.is_playing else QColor(46, 213, 200)

        star_radius = int(15 * 0.5)  # 缩小50%，半径约7.5px
        self.draw_star(painter, 15, 15, star_radius, star_color)




class Login(QWidget):
    def __init__(self, main_window):
        super(Login, self).__init__()
        self.main_window = main_window
        self.main_window.message_hub.signals.user_login_response.connect(self.login_response)
        self.login_flag = False
        self.init()
        self.main_window.logger.info('初始化登入窗口')

    def init(self):
        self.setWindowTitle("欢迎加入超级四国大战！")
        self.setWindowIcon(QIcon('./client/image/4InWar.ico'))
        self.setWindowFlags(Qt.WindowType.WindowMinimizeButtonHint | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(400, 260)
        MainWindow.center(self)
        self.show()

        logo = QLabel()
        logo.setPixmap(QPixmap("./client/image/logo.png"))
        logo.resize(400, 150)
        hbox1 = QHBoxLayout()
        hbox1.addWidget(logo)
        self.name = QLineEdit()
        self.name.setMaximumWidth(180)
        self.pwd = QLineEdit()
        self.pwd.setMaximumWidth(180)
        layout = QFormLayout()
        layout.setFormAlignment(Qt.AlignmentFlag.AlignHCenter)
        layout.addRow('用户名', self.name)
        layout.addRow('密　码', self.pwd)
        self.name.setEchoMode(QLineEdit.EchoMode.Normal)
        self.name.setPlaceholderText("请输入1-8个中文字符")
        self.pwd.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd.setPlaceholderText("请输入密码")
        n = QRegularExpression(r'[\p{Han}]{1,8}')
        p = QRegularExpression(r'[a-zA-Z0-9]+')
        v1 = QRegularExpressionValidator(self)
        v2 = QRegularExpressionValidator(self)
        v1.setRegularExpression(n)
        v2.setRegularExpression(p)
        self.name.setValidator(v1)
        self.pwd.setValidator(v2)
        self.reg = QPushButton('注册')
        self.log = QPushButton('登入')
        self.log.setFocus()
        self.log.setDefault(True)
        self.log.setShortcut(QKeySequence(Qt.Key.Key_Return))
        self.reg.setMaximumWidth(105)
        self.log.setMaximumWidth(105)
        self.reg.clicked.connect(self.reg_page)
        self.log.clicked.connect(self.login)
        hbox2 = QHBoxLayout()
        hbox2.addSpacing(90)
        hbox2.addWidget(self.reg)
        hbox2.addSpacing(3)
        hbox2.addWidget(self.log)
        hbox2.addSpacing(80)
        vbox = QVBoxLayout()
        vbox.addLayout(hbox1)
        vbox.addSpacing(13)
        vbox.addLayout(layout)
        vbox.addLayout(hbox2)
        vbox.setContentsMargins(0, 0, 0, 10)
        self.setLayout(vbox)

    def reg_page(self):
        self.hide()
        self.name.clear()
        self.pwd.clear()
        self.register = Register(self)
        if self.register.exec():
            pass
        self.show()

    def closeEvent(self, event):
        self.main_window.logger.info('捕获到登入窗口关闭事件')
        if not self.login_flag and self.main_window.login_widget_on:
            event.ignore()
            self.main_window.close()
        else:
            event.accept()

    def login(self):
        str_name = self.name.text()
        print(str_name)
        str_pwd = self.pwd.text()
        print(str_pwd)
        if len(str_name) == 0:
            QMessageBox.warning(self, "登入错误！", "必须输入用户名，请重试。")
            return
        elif len(str_pwd) == 0:
            QMessageBox.warning(self, "登入错误！", "必须输入密码，请重试。")
            return
        self.reg.setEnabled(False)
        self.log.setEnabled(False)
        # 使用SHA256进行客户端哈希（确定性哈希，不使用随机盐）
        import hashlib
        password_hash = hashlib.sha256(str_pwd.encode('utf-8')).hexdigest()
        message = {
            'type': 'user_login',
            'data': {
                'username': str_name,
                'password': password_hash
            }
        }
        self.main_window.message_hub.send_to_business_thread(message)
        print('登入中...')

    def login_response(self, message: dict):
        # 正确提取服务器响应数据
        data = message.get('data', {})
        success = data.get('success', False)
        error = data.get('error', '')

        print(f"[Login] 收到登录响应: success={success}, error={error}")

        if success:
            self.login_flag = True
            self.close()
            self.main_window.show()
            self.main_window.logger.info('登入成功，显示主窗口')
            self.main_window.user_data = data.get('user_data', {})
        else:
            # 根据错误信息判断错误类型
            if '用户名' in error or '不存在' in error:
                QMessageBox.warning(self, "登入错误！", "此用户名尚未注册，请先注册后再登入。")
                self.name.setText("")
                self.pwd.setText("")
            elif '禁用' in error:
                QMessageBox.warning(self, "登入错误！", "此账户已被禁用，请联系管理员。")
            elif '密码' in error:
                QMessageBox.warning(self, "登入错误！", "密码错误，请重新输入。")
                self.pwd.setText("")
            else:
                QMessageBox.warning(self, "登入错误！", error)

            self.reg.setEnabled(True)
            self.log.setEnabled(True)


class Register(QDialog):
    def __init__(self, login_widget):
        super().__init__()
        self.logger = login_widget.main_window.logger
        self.login_widget = login_widget
        self.login_widget.main_window.message_hub.signals.user_register_response.connect(self.register_response)
        self.init()

    def init(self):
        self.setWindowTitle("欢迎注册超级四国大战！")
        self.setWindowIcon(QIcon('./client/image/4InWar.ico'))
        self.setWindowFlags(Qt.WindowType.WindowMinimizeButtonHint | Qt.WindowType.WindowCloseButtonHint)
        self.setFixedSize(400, 180)
        MainWindow.center(self)
        self.name = QLineEdit()
        self.name.setMaximumWidth(180)
        self.pwd = QLineEdit()
        self.pwd.setMaximumWidth(180)
        self.pwd1 = QLineEdit()
        self.pwd1.setMaximumWidth(180)
        self.invitation = QLineEdit()
        self.invitation.setMaximumWidth(180)
        layout = QFormLayout()
        layout.setFormAlignment(Qt.AlignmentFlag.AlignHCenter)
        layout.addRow('用　户　名', self.name)
        layout.addRow('密　　　码', self.pwd)
        layout.addRow('请确认密码', self.pwd1)
        layout.addRow('邀　请　码', self.invitation)
        self.name.setEchoMode(QLineEdit.EchoMode.Normal)
        self.name.setPlaceholderText("请输入1-8个中文字符")
        self.pwd.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd.setPlaceholderText("只允许数字和英文")
        self.pwd1.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd1.setPlaceholderText("只允许数字和英文")
        self.invitation.setEchoMode(QLineEdit.EchoMode.Normal)
        self.invitation.setPlaceholderText("请输入邀请码")
        n = QRegularExpression(r'[\p{Han}]{1,8}')
        p = QRegularExpression(r'[a-zA-Z0-9]+')
        v1 = QRegularExpressionValidator(self)
        v1.setRegularExpression(n)
        v2 = QRegularExpressionValidator(self)
        v2.setRegularExpression(p)
        self.name.setValidator(v1)
        self.pwd.setValidator(v2)
        self.pwd1.setValidator(v2)
        self.reg = QPushButton('注册')
        self.reg.setMaximumWidth(105)
        self.reg.clicked.connect(self.register)
        self.cancel = QPushButton('取消')
        self.cancel.setMaximumWidth(105)
        self.cancel.clicked.connect(self.close)
        hbox = QHBoxLayout()
        hbox.addSpacing(90)
        hbox.addWidget(self.reg)
        hbox.addSpacing(3)
        hbox.addWidget(self.cancel)
        hbox.addSpacing(80)
        vbox = QVBoxLayout()
        vbox.addLayout(layout)
        vbox.addLayout(hbox)
        vbox.setContentsMargins(0, 10, 0, 10)
        self.setLayout(vbox)

    def register(self):
        str_name = self.name.text()
        print(len(str_name))
        str_pwd = self.pwd.text()
        print(str_pwd)
        str_pwd1 = self.pwd1.text()
        print(str_pwd1)
        str_invitation = self.invitation.text()
        print(str_invitation)
        if len(str_name) == 0:
            QMessageBox.warning(self, "注册错误！", "必须设置用户名，请重试。")
            return
        elif len(str_pwd) == 0:
            QMessageBox.warning(self, "注册错误！", "必须设置密码，请重试。")
            return
        elif len(str_pwd1) == 0:
            QMessageBox.warning(self, "注册错误！", "必须输入验证密码，请重试。")
            return
        elif str_pwd != str_pwd1:
            QMessageBox.warning(self, "注册错误！", "两次输入的密码不相同，请重试。")
            self.pwd.setText("")
            self.pwd1.setText("")
            return
        elif len(str_invitation) == 0:
            QMessageBox.warning(self, "注册错误！", "必须输入邀请码，请重试。")
            return
            
        self.reg.setEnabled(False)
        self.cancel.setEnabled(False)
        self.name.setEnabled(False)
        self.pwd.setEnabled(False)
        self.pwd1.setEnabled(False)
        self.invitation.setEnabled(False)
        # 使用SHA256进行客户端哈希（与登录保持一致）
        password_hash = hashlib.sha256(str_pwd.encode('utf-8')).hexdigest()
        message = {
            'type': 'user_register',
            'data': {
                'username': str_name,
                'password': password_hash,
                'invitation': str_invitation
            }
        }
        self.login_widget.main_window.message_hub.send_to_business_thread(message)
        print('注册中...')

    def register_response(self, message: dict):
        self.reg.setEnabled(True)
        self.cancel.setEnabled(True)
        self.name.setEnabled(True)
        self.pwd.setEnabled(True)
        self.pwd1.setEnabled(True)
        self.invitation.setEnabled(True)

        # 正确提取服务器响应数据
        data = message.get('data', {})
        response_type = data.get('type')
        response_message = data.get('message')

        print(f"[Register] 收到注册响应: type={response_type}, message={response_message}")

        if response_type == 'success' and response_message:
            QMessageBox.information(self, "注册成功！", response_message)
            self.close()
        elif response_type and response_message:
            QMessageBox.warning(self, "注册错误！", response_message)
            if response_type == 'wrong_invitation':
                self.invitation.setText("")
            elif response_type == 'same_name':
                self.name.setText("")
                self.invitation.setText("")


class MainWindow(QMainWindow):  # 管理主窗口及相关组件

    def __init__(self, app, logger, message_hub):
        super().__init__()
        self.app = app
        self.logger = logger
        self.message_hub = message_hub
        self.message_hub.mainwindow = self
        self.rooms_dict = {}  # {room_name: room_widget} 房间名到房间对象的映射
        self.user_data = {}

        # 确保头像缓存目录存在
        cache_dir = "./client/image/cache/avatars"
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

        self.login_widget = Login(self)
        self.login_widget_on = True
        self.logger.info('初始化主窗口')
        self.init_ui()  # 用来实例化UI对象
        self.resize_ui()  # 对UI对象的缩放统一管理
        self._is_connected = False
        self.I_am_playing = False
        self.logger.info('主窗口初始化完成')

    # 初始化创建UI对象，并作一些初始设置
    def init_ui(self):
        # 根据不同的桌面分辨率，确定适当的缩放系数
        screen = self.app.primaryScreen()
        self.scale = (screen.geometry().height() - 100) / 945 / 1
        self.logger.debug(f'设置窗口缩放系数: {self.scale}')
        
        # 创建主窗口，并设置标题及图标
        self.setWindowTitle("超级四国大战")
        self.setWindowIcon(QIcon(r'./client/image/4InWar.ico'))
        self.setStyleSheet("background-color: #2b2b2b; color: #ffffff;")
        
        # 创建左侧堆叠组件,用来装载下面的页面
        self.left_stack = QStackedWidget(self)

        # 第一页：大厅页面
        self.hall = QWidget()

        # 设置暗色调背景
        self.hall.setStyleSheet("background-color: #2b2b2b; color: #ffffff;  border: None;")

        hall_main_layout = QVBoxLayout(self.hall)
        hall_main_layout.setContentsMargins(0, 0, 0, 0)
        hall_main_layout.setSpacing(0)

        # 顶部标题区域 - 使用重叠的QLabel避免CSS继承问题
        title_widget = QWidget()
        title_widget.setFixedHeight(180)
        title_widget.setStyleSheet("background-color: transparent;")  # 容器透明

        # 背景图片QLabel - 使用布局管理器而不是固定几何位置
        background_label = QLabel(title_widget)

        # 创建布局管理器让背景图片自适应容器大小
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(20, 10, 5, 0)
        title_layout.addWidget(background_label)

        # 加载PNG格式的背景图片
        pixmap = QPixmap("./client/image/banner.png")  # 使用PNG格式
        if not pixmap.isNull():
            background_label.setPixmap(pixmap)
            background_label.setScaledContents(True)  # 自动缩放图片
        else:
            # 如果图片加载失败，使用纯色背景
            background_label.setStyleSheet("background-color: #3c3c3c;")

        # 文字标题QLabel（重叠在背景上，透明背景）
        title_label = QLabel("游戏大厅", title_widget)
        title_label.setGeometry(50, 30, 400, 40)  # 绝对定位
        title_label.setStyleSheet("font-size: 33px; font-weight: Bold; color: #ffffff; background-color: transparent;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # 副标题QLabel（重叠在背景上，透明背景）
        subtitle_label = QLabel("欢迎加入超级四国大战", title_widget)
        subtitle_label.setGeometry(50, 80, 400, 40)  # 绝对定位，下移到90
        subtitle_label.setStyleSheet("font-size: 13px; color: #cccccc; background-color: transparent;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

        hall_main_layout.addWidget(title_widget)

        # 房间区域容器
        rooms_container = QWidget()
        rooms_container.setStyleSheet("background-color: #2b2b2b;")
        rooms_layout = QHBoxLayout(rooms_container)
        rooms_layout.setContentsMargins(10, 10, 5, 0)
        rooms_layout.setSpacing(15)

        # 左侧：正在进行的游戏房间
        playing_rooms_widget, self.playing_rooms_layout = self.create_room_list_widget("正在进行的游戏", "#a06c44")
        rooms_layout.addWidget(playing_rooms_widget)

        # 右侧：准备中的游戏房间
        waiting_rooms_widget, self.waiting_rooms_layout = self.create_room_list_widget("准备中的游戏", "#2fd5c9")
        rooms_layout.addWidget(waiting_rooms_widget)

        hall_main_layout.addWidget(rooms_container, 1)  # 占用剩余空间

        self.left_stack.addWidget(self.hall)

        # 第二页：游戏视图
        
        # 创建场景用来布置游戏棋盘区域
        self.scene = GameScene(self.logger, self.message_hub.signals.scene_click_signal, './client/image/chessboard.svg')
        self.view = QGraphicsView()
        self.view.setFrameStyle(QFrame.Shape.NoFrame)
        self.view.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 这两行避免显示view窗口的滑动条
        self.view.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.view.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
        self.view.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        self.view.setScene(self.scene)  # 给视图窗口设置需显示的场景,默认场景总是以中心点对齐视口中心点的。
        self.left_stack.addWidget(self.view)

        # 默认显示第一页
        self.left_stack.setCurrentIndex(0)

        # 创建右侧标签页
        self.right_tabs = QTabWidget(self)

        # 设置标签页样式 - 让标签按键更大更明显，使用系统颜色
        self.right_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 0px solid palette(mid);
                background-color: palette(window);
                padding: 0px;
            }

            QTabWidget::tab-bar {
                alignment: left;
                top: 5px;
            }

            QTabBar::tab {
                background-color: #514236;
                border: 0px solid palette(mid);
                border-bottom-color: palette(mid);
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                min-width: 60px;
                min-height: 15px;
                padding: 6px 8px;
                margin-right: 3px;
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
            }

            QTabBar::tab:selected {
                background-color: #a06c44;
                color: palette(highlighted-text);
                border-color: palette(highlight);
                border-bottom-color: palette(highlight);
                font-size: 15px;
            }

            QTabBar::tab:hover {
                background-color: #036c86;
                color: #ffffff;
                font-size: 15px;
            }

            QTabBar::tab:!selected {
                margin-top: 3px;
            }
        """)

        # 连接标签页切换信号
        self.right_tabs.currentChanged.connect(self.on_tab_changed)

        # 创建游戏大厅标签页
        self.game_hall_tab = self.create_game_hall_tab()
        self.right_tabs.addTab(self.game_hall_tab, "游戏大厅")

        # 创建房间座位标签页（初始化时不添加到标签页中）
        self.room_tab, self.chat_display = self.create_room_tab()
        self.right_tabs.addTab(self.room_tab, "房间名称")
        # 需要显示时调用：self.right_tabs.addTab(self.room_tab, "房间名称")

        self.user_frame = QFrame()
        self.user_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        user_layout = QVBoxLayout(self.user_frame)
        user_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        user_layout.setSpacing(0)  # 无间距
        
        self.avatar_label = QLabel(self.user_frame)
        pixmap = QPixmap("./client/image/user1.png")
        self.avatar_label.setPixmap(pixmap)
        self.user_info_label = QLabel(self.user_frame)

        user_layout.addWidget(self.avatar_label)
        user_layout.addWidget(self.user_info_label)

        '''
        模拟创建一些房间，展示各种状态
        '''
        self._simulate_various_room_states()

    def show_room_tab(self, room_name):
        self.right_tabs.addTab(self.room_tab, room_name)
        self.chat_display.clear()

    def on_tab_changed(self, index):
        """标签页切换事件处理"""
        tab_text = self.right_tabs.tabText(index)
        self.logger.info(f'标签页切换到: {tab_text} (索引: {index})')

        # 根据不同标签页控制左侧堆叠组件显示
        if index == 0:  # 游戏大厅标签页
            self.logger.debug('切换到游戏大厅标签页 - 显示欢迎页面')
            self.left_stack.setCurrentIndex(0)  # 显示欢迎页面
        elif index == 1:  # 房间信息标签页
            self.logger.debug('切换到房间信息标签页 - 显示游戏视图')
            self.left_stack.setCurrentIndex(1)  # 显示游戏视图

    def create_game_hall_tab(self):
        """创建游戏控制标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(0, 10, 0, 10)  # 去除标签页内容的内边距

        # 创建功能区
        function_frame = QFrame()
        function_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        function_frame.setLineWidth(0)
        function_frame.setMidLineWidth(0)

        # 创建按钮
        self.lock_bt = QPushButton("锁定座位")
        self.lock_bt.setEnabled(False)
        self.unlock_bt = QPushButton("解锁座位")
        self.unlock_bt.setEnabled(False)
        self.re_size_bt = QPushButton('窗口变小')
        self.re_size_bt.setEnabled(False)
        self.quit_bt = QPushButton('退出游戏')
        self.quit_bt.clicked.connect(self.close)

        # 设置按键字体为雅黑
        for button in [self.lock_bt, self.unlock_bt, self.re_size_bt, self.quit_bt]:
            button_font = button.font()
            button_font.setFamily("Microsoft YaHei")
            button.setFont(button_font)

        # 布局
        vb1 = QVBoxLayout(function_frame)
        vb1.setAlignment(Qt.AlignmentFlag.AlignTop)
        vb1.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        vb1.setSpacing(6)  # 功能区按键之间增加间距

        hb1 = QHBoxLayout()
        hb1.setContentsMargins(0, 0, 0, 0)
        hb1.addWidget(self.lock_bt)
        hb1.addWidget(self.unlock_bt)

        hb2 = QHBoxLayout()
        hb2.setContentsMargins(0, 0, 0, 0)
        hb2.addWidget(self.re_size_bt)

        hb3 = QHBoxLayout()
        hb3.setContentsMargins(0, 0, 0, 0)
        hb3.addWidget(self.quit_bt)

        vb1.addLayout(hb1)
        vb1.addLayout(hb2)
        vb1.addLayout(hb3)

        layout.addWidget(function_frame)

        # 添加弹性空间，为以后插入其他frame预留位置
        layout.addStretch()

        # 添加组件间距
        layout.addSpacing(12)

        # 添加聊天界面（置底，自适应向上扩展）
        chat_interface, chat_display = self.create_chat_interface("大厅聊天室")
        layout.addWidget(chat_interface, 1)  # stretch factor = 1，允许向上扩展

        return tab

    def create_room_tab(self):
        """创建房间信息标签页"""
        tab = QWidget()
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(0, 10, 0, 10)  # 去除标签页内容的内边距

        # 创建顶部水平布局，用于左右排列功能区和包厢区
        top_layout = QHBoxLayout()

        # 创建功能区（左侧）
        function_frame = QFrame()
        function_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        function_frame.setLineWidth(0)
        function_frame.setMidLineWidth(0)

        # 创建按钮
        self.lock_bt = QPushButton("锁定座位")
        self.lock_bt.setEnabled(False)
        self.unlock_bt = QPushButton("解锁座位")
        self.unlock_bt.setEnabled(False)
        self.re_size_bt = QPushButton('窗口变小')
        self.re_size_bt.setEnabled(False)
        self.quit_bt = QPushButton('退出游戏')
        self.quit_bt.clicked.connect(self.close)

        # 设置按键字体为雅黑
        for button in [self.lock_bt, self.unlock_bt, self.re_size_bt, self.quit_bt]:
            button_font = button.font()
            button_font.setFamily("Microsoft YaHei")
            button.setFont(button_font)

        # 功能区布局
        function_layout = QVBoxLayout(function_frame)
        function_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        function_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        function_layout.setSpacing(6)  # 功能区按键之间增加间距

        # 按钮行布局
        button_row1 = QHBoxLayout()
        button_row1.addWidget(self.lock_bt)
        button_row1.addWidget(self.unlock_bt)

        button_row2 = QHBoxLayout()
        button_row2.addWidget(self.re_size_bt)

        button_row3 = QHBoxLayout()
        button_row3.addWidget(self.quit_bt)

        function_layout.addLayout(button_row1)
        function_layout.addLayout(button_row2)
        function_layout.addLayout(button_row3)

        # 创建包厢区（右侧）
        self.room = QFrame()
        self.room.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        self.room.setLineWidth(1)
        self.room.setMidLineWidth(0)

        # 创建座位
        self.s0 = Seat(0)
        self.s1 = Seat(1)
        self.s2 = Seat(2)
        self.s3 = Seat(3)
        self.seats = [self.s0, self.s1, self.s2, self.s3]
        self.table = QLabel()

        # 座位布局
        room_grid = QGridLayout(self.room)
        room_grid.setSpacing(0)
        room_grid.addWidget(self.s0, 0, 1)
        room_grid.addWidget(self.s1, 1, 0)
        room_grid.addWidget(self.s3, 1, 2)
        room_grid.addWidget(self.s2, 2, 1)
        room_grid.addWidget(self.table, 1, 1)

        # 将功能区和包厢区添加到顶部水平布局
        top_layout.addWidget(function_frame)
        top_layout.addWidget(self.room)

        # 将顶部布局添加到主布局
        main_layout.addLayout(top_layout)

        # 添加弹性空间，为以后插入其他frame预留位置
        main_layout.addStretch()

        # 添加组件间距
        main_layout.addSpacing(12)

        # 添加聊天界面（置底，自适应向上扩展）
        chat_interface, chat_display = self.create_chat_interface("房间聊天室")
        main_layout.addWidget(chat_interface, 1)  # stretch factor = 1，允许向上扩展

        return tab, chat_display
    
    def create_chat_interface(self, tab_name=""):
        """创建聊天界面"""
        chat_frame = QFrame()

        chat_layout = QVBoxLayout(chat_frame)
        chat_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        chat_layout.setSpacing(8)  # 聊天显示框和输入框之间增加间距

        chat_name = QLabel(tab_name)
        chat_name.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # 设置聊天标题字体大小和颜色
        title_font = chat_name.font()
        title_font.setPointSize(14)  # 设置较大的字体大小
        title_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        title_font.setWeight(QFont.Weight.Bold)  # 设置粗体
        chat_name.setFont(title_font)
        chat_name.setStyleSheet("color: #888888;")  # 设置标题颜色

        chat_layout.addWidget(chat_name)

        # 聊天显示区
        chat_display = QTextEdit()
        chat_display.setReadOnly(True)
        chat_display.setMinimumHeight(150)  # 设置最小高度，确保基本可用性
        # 移除最大高度限制，允许自适应扩展
        chat_display.setPlaceholderText("聊天记录将在这里显示...")

        # 设置聊天显示区字体大小
        chat_font = chat_display.font()
        chat_font.setPointSize(10)  # 增大字体
        chat_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        chat_font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1.2)  # 设置字符间距，增加1.2像素
        chat_display.setFont(chat_font)

        # 设置聊天显示区样式（圆角处理）
        chat_display.setStyleSheet("""
            QTextEdit {
                border-radius: 4px;
                padding: 10px;
                background-color: #333333;
                color: #ffffff;
                border: 0px solid #555555;
            }
        """)

        # 添加一些示例消息（身份提示加粗换色，增加行距）
        chat_display.append('<div style="margin-bottom: 4px;"><b style="color: #d32f2f; font-size: 15px; font-weight: bold;">系统 :</b> 欢迎进入4inWar游戏!</div>')
        chat_display.append('<div style="margin-bottom: 4px;"><b style="color: #1976d2; font-size: 15px; font-weight: bold;">玩家1 :</b> 大家好!</div>')

        # 输入区域
        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(0, 0, 0, 0)  # 设置边距与聊天显示区一致
        input_layout.setSpacing(8)  # 输入框和发送按钮之间的间距

        # 聊天输入框
        chat_input = QLineEdit()
        chat_input.setPlaceholderText("输入消息...")

        # 设置输入框高度和字体
        chat_input.setMinimumHeight(32)  # 增加输入框高度
        input_font = chat_input.font()
        input_font.setPointSize(10)  # 增大字体
        input_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        input_font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1.2)  # 设置字符间距，增加1.2像素
        chat_input.setFont(input_font)

        # 设置输入框样式（圆角处理和背景色）
        chat_input.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                border-radius: 4px;
                padding: 4px;
                color: #ffffff;
                border: 0px solid #555555;
            }
        """)

        # 发送按钮
        send_button = QPushButton("发送")
        send_button.setMinimumWidth(80)  # 设置按钮最小宽度
        send_button.setMinimumHeight(32)  # 与输入框高度匹配

        # 设置按钮字体
        button_font = send_button.font()
        button_font.setPointSize(11)  # 增大字体
        button_font.setBold(True)  # 加粗
        button_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        send_button.setFont(button_font)

        # 设置按钮样式（包括文字颜色和hover效果）
        send_button.setStyleSheet("""
            QPushButton {
                color: white;
                background-color: #a06c44;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #036c86;
                font-size: 16px;
            }
        """)

        # 创建发送消息的函数
        def send_message():
            message = chat_input.text().strip()
            if message:
                # 添加消息到显示区（身份提示加粗换色，增加行距）
                chat_display.append(f'<div style="margin-bottom: 4px;"><b style="color: #a06c44; font-size: 15px; font-weight: bold;">我 :</b> {message}</div>')
                # 清空输入框
                chat_input.clear()
                # 滚动到底部
                chat_display.verticalScrollBar().setValue(
                    chat_display.verticalScrollBar().maximum()
                )

        # 绑定发送事件
        chat_input.returnPressed.connect(send_message)
        send_button.clicked.connect(send_message)

        input_layout.addWidget(chat_input)
        input_layout.addWidget(send_button)

        # 添加到聊天布局
        chat_layout.addWidget(chat_display, 1)  # stretch factor = 1，聊天显示区占用剩余空间
        chat_layout.addLayout(input_layout)  # 输入区固定在底部

        return chat_frame, chat_display


    def create_room_list_widget(self, title, title_color):
        """创建房间列表组件，返回容器widget和房间列表布局"""
        container = QWidget()
        container.setStyleSheet(f"background-color: #2b2b2b; border: none;")

        layout = QVBoxLayout(container)
        layout.setContentsMargins(10, 10, 0, 10)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {title_color}; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(title_label)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: #333333;
                width: 8px;
                border-radius: 2px;
                border: none;
            }}
            QScrollBar::handle:vertical {{
                background-color: #4d4d4d;
                border-radius: 2px;
                min-height: 20px;
                border: none;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {title_color};
            }}
            QScrollBar::add-line:vertical {{
                background: none;
                height: 0px;
                border: none;
            }}
            QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
                border: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: #3c3c3c;
                border: none;
            }}
        """)

        # 房间列表容器
        rooms_list_widget = QWidget()
        rooms_list_layout = QVBoxLayout(rooms_list_widget)
        rooms_list_layout.setContentsMargins(0, 0, 10, 0)  # 右侧增加10px间距，避免紧贴滚动条
        rooms_list_layout.setSpacing(10)
        rooms_list_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 设置向上对齐
        scroll_area.setWidget(rooms_list_widget)
        layout.addWidget(scroll_area, 1)
        return container, rooms_list_layout


    # 服务器通知驱动的房间管理方法（统一使用房间名作为参数）
    def create_playing_room(self, room_name, status=1):
        """创建正在进行的游戏房间（服务器通知）"""
        if hasattr(self, 'playing_rooms_layout') and room_name not in self.rooms_dict:
            room_widget = RoomItemWidget(self.logger, room_name, status, self)
            self.playing_rooms_layout.addWidget(room_widget)
            self.rooms_dict[room_name] = room_widget
            return room_widget
        return None

    def create_waiting_room(self, room_name, status=0):
        """创建准备中的游戏房间（服务器通知）"""
        if hasattr(self, 'waiting_rooms_layout') and room_name not in self.rooms_dict:
            room_widget = RoomItemWidget(self.logger, room_name, status, self)
            self.waiting_rooms_layout.addWidget(room_widget)
            self.rooms_dict[room_name] = room_widget
            return room_widget
        return None

    def remove_room(self, room_name):
        """根据房间名移除房间（服务器通知）"""
        room_widget = self.rooms_dict.get(room_name)
        if not room_widget:
            return False

        # 从字典中移除
        del self.rooms_dict[room_name]

        # 根据房间的游戏状态判断从哪个布局移除
        if hasattr(room_widget, 'is_playing') and room_widget.is_playing:
            # 正在游戏的房间，从左侧列表移除
            if hasattr(self, 'playing_rooms_layout'):
                self.playing_rooms_layout.removeWidget(room_widget)
        else:
            # 准备中的房间，从右侧列表移除
            if hasattr(self, 'waiting_rooms_layout'):
                self.waiting_rooms_layout.removeWidget(room_widget)

        # 删除widget对象
        room_widget.deleteLater()
        return True

    def move_room_to_playing(self, room_name):
        """将准备房间移动到游戏列表（服务器通知游戏开始）"""
        if room_name in self.rooms_dict:
            # 移除原房间
            self.remove_room(room_name)
            # 创建新的游戏房间
            return self.create_playing_room(room_name, 1)
        return None

    def move_room_to_waiting(self, room_name):
        """将游戏房间移动到准备列表（服务器通知游戏结束）"""
        if room_name in self.rooms_dict:
            # 移除原房间
            self.remove_room(room_name)
            # 创建新的等待房间
            return self.create_waiting_room(room_name, 0)
        return None

    def update_room_info(self, room_name, new_players_info, new_status=None):
        """更新房间信息（服务器通知）"""
        room_widget = self.rooms_dict.get(room_name)
        if room_widget:
            # 更新房间显示信息
            if hasattr(room_widget, 'update_info'):
                room_widget.update_info(new_players_info, new_status)
            return True
        return False

    def get_room_by_name(self, room_name):
        """根据房间名获取房间对象"""
        return self.rooms_dict.get(room_name)

    # 服务器消息处理方法
    def handle_room_event(self, message):
        """处理房间相关事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")

        if event_type == "player_enter_room":
            self._handle_player_enter_room(data)
        elif event_type == "player_leave_room":
            self._handle_player_leave_room(data)
        elif event_type == "seat_locked":
            self._handle_seat_locked(data)
        elif event_type == "seat_unlocked":
            self._handle_seat_unlocked(data)
        elif event_type == "game_started":
            self._handle_game_started(data)

    def _handle_player_enter_room(self, data):
        """处理玩家进入房间消息"""
        room_name = data.get("room_name")
        player_name = data.get("player_name")

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.add_room_member(player_name)

    def _handle_player_leave_room(self, data):
        """处理玩家离开房间消息"""
        room_name = data.get("room_name")
        player_name = data.get("player_name")

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.remove_room_member(player_name)

    def _handle_seat_locked(self, data):
        """处理座位锁定消息（增量更新）"""
        room_name = data.get("room_name")
        seat_index = data.get("seat_index")  # 0, 1, 2, 3
        user_info = data.get("user_info")  # {"user_id": "123", "user_name": "Alice"}

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.lock_seat(seat_index, user_info)

    def _handle_seat_unlocked(self, data):
        """处理座位解锁消息（增量更新）"""
        room_name = data.get("room_name")
        seat_index = data.get("seat_index")

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.unlock_seat(seat_index)

    def _handle_game_started(self, data):
        """处理游戏开始消息"""
        room_name = data.get("room_name")

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            # 保存当前座位锁定信息
            current_seat_locks = room_widget.seat_locks.copy()

            # 移动房间到游戏列表
            new_room_widget = self.move_room_to_playing(room_name)

            # 将座位锁定信息复制到新的游戏房间
            if new_room_widget:
                for seat_index, user_info in current_seat_locks.items():
                    new_room_widget.lock_seat(seat_index, user_info)

    def _simulate_various_room_states(self):
        """模拟各种房间状态"""
        # 模拟用户数据（部分用户使用cache中的头像）
        mock_users = [
            {"user_id": "001", "user_name": "Alice"},
            {"user_id": "user2", "user_name": "Bob"},      # 使用cache中的user2.png
            {"user_id": "user3", "user_name": "Charlie"},  # 使用cache中的user3.png
            {"user_id": "004", "user_name": "David"},
            {"user_id": "005", "user_name": "Eve"},
            {"user_id": "006", "user_name": "Frank"},
            {"user_id": "007", "user_name": "Grace"},
            {"user_id": "008", "user_name": "Henry"},
        ]

        # 创建等待房间，展示不同的座位锁定状态（包含同队情况）
        waiting_rooms = [
            {"name": "room_01", "locked_seats": {}},  # 空房间
            {"name": "room_02", "locked_seats": {0: mock_users[0]}},  # 1人
            {"name": "room_03", "locked_seats": {0: mock_users[1], 2: mock_users[2]}},  # 2人同队（0和2是队友）
            {"name": "room_04", "locked_seats": {1: mock_users[3], 3: mock_users[4]}},  # 2人同队（1和3是队友）
            {"name": "room_05", "locked_seats": {0: mock_users[5], 1: mock_users[6], 2: mock_users[7]}},  # 3人（包含一对队友）
            {"name": "room_06", "locked_seats": {2: mock_users[0], 3: mock_users[1]}},  # 2人不同队（2和3是对手）
            {"name": "room_07", "locked_seats": {3: mock_users[2], 1: mock_users[3], 0: mock_users[4]}},  # 3人（包含一对队友1和3）
            {"name": "room_08", "locked_seats": {1: mock_users[5]}},  # 1人在座位1
            {"name": "room_09", "locked_seats": {3: mock_users[6], 1: mock_users[7]}},  # 2人同队（1和3是队友）
            {"name": "room_10", "locked_seats": {2: mock_users[0], 1: mock_users[1], 3: mock_users[2]}},  # 3人（包含一对队友1和3）
            {"name": "room_11", "locked_seats": {3: mock_users[3], 2: mock_users[4], 0: mock_users[5]}},  # 3人（包含一对队友0和2）
        ]

        # 创建等待房间
        for room_data in waiting_rooms:
            room_name = room_data["name"]
            # 创建空房间
            room_widget = self.create_waiting_room(room_name, 0)

            # 模拟座位锁定
            if room_widget:
                for seat_index, user_info in room_data["locked_seats"].items():
                    room_widget.lock_seat(seat_index, user_info)

        # 创建更多等待房间（包含同队座位的情况）
        import random
        for i in range(12, 20):
            room_name = f"room_{i:02d}"
            room_widget = self.create_waiting_room(room_name, 0)

            if room_widget:
                # 随机选择不同的座位锁定模式
                mode = random.randint(1, 6)

                if mode == 1:
                    # 单人随机座位
                    seat = random.randint(0, 3)
                    user_info = mock_users[random.randint(0, len(mock_users)-1)]
                    room_widget.lock_seat(seat, user_info)

                elif mode == 2:
                    # 同队座位：0和2
                    user1 = mock_users[random.randint(0, len(mock_users)-1)]
                    user2 = mock_users[random.randint(0, len(mock_users)-1)]
                    room_widget.lock_seat(0, user1)
                    room_widget.lock_seat(2, user2)

                elif mode == 3:
                    # 同队座位：1和3
                    user1 = mock_users[random.randint(0, len(mock_users)-1)]
                    user2 = mock_users[random.randint(0, len(mock_users)-1)]
                    room_widget.lock_seat(1, user1)
                    room_widget.lock_seat(3, user2)

                elif mode == 4:
                    # 对手座位：0和1
                    user1 = mock_users[random.randint(0, len(mock_users)-1)]
                    user2 = mock_users[random.randint(0, len(mock_users)-1)]
                    room_widget.lock_seat(0, user1)
                    room_widget.lock_seat(1, user2)

                elif mode == 5:
                    # 对手座位：2和3
                    user1 = mock_users[random.randint(0, len(mock_users)-1)]
                    user2 = mock_users[random.randint(0, len(mock_users)-1)]
                    room_widget.lock_seat(2, user1)
                    room_widget.lock_seat(3, user2)

                else:
                    # 3人房间（包含一对队友）
                    seats = random.choice([(0, 1, 2), (0, 2, 3), (1, 2, 3), (0, 1, 3)])
                    for j, seat in enumerate(seats):
                        user_info = mock_users[(i + j) % len(mock_users)]
                        room_widget.lock_seat(seat, user_info)

        # 创建游戏中房间（使用座位锁定机制）- 15个房间
        playing_rooms = []

        # 预定义的玩家名称池
        player_names = [
            "Alice", "Bob", "Charlie", "David", "Eve", "Frank", "Grace", "Henry",
            "Ivy", "Jack", "Kate", "Leo", "Mary", "Nick", "Olivia", "Paul",
            "Quinn", "Rose", "Sam", "Tom", "Uma", "Victor", "Wendy", "Xavier",
            "Yuki", "Zoe", "Alex", "Beth", "Chris", "Diana", "Eric", "Fiona",
            "George", "Helen", "Ian", "Julia", "Kevin", "Luna", "Mike", "Nina",
            "Oscar", "Penny", "Quincy", "Ruby", "Steve", "Tina", "Ulrich", "Vera",
            "Will", "Xara", "Yale", "Zara", "Aaron", "Bella", "Carl", "Dora",
            "Ethan", "Flora", "Gary", "Hana"
        ]

        # 生成15个游戏房间
        for i in range(1, 16):
            room_name = f"game_{i:02d}"
            locked_seats = {}

            # 为每个房间分配4个玩家
            for seat in range(4):
                player_index = (i - 1) * 4 + seat
                user_name = player_names[player_index % len(player_names)]

                # 特定玩家使用cache中的头像
                if user_name == "Bob":
                    user_id = "user2"  # 使用cache中的user2.png
                elif user_name == "Charlie":
                    user_id = "user3"  # 使用cache中的user3.png
                else:
                    user_id = f"{200 + player_index:03d}"

                # 如果名字重复，添加数字后缀
                if player_index >= len(player_names):
                    suffix = (player_index // len(player_names)) + 1
                    user_name = f"{user_name}{suffix}"
                    # 对于重复名字，仍使用数字ID
                    user_id = f"{200 + player_index:03d}"

                locked_seats[seat] = {
                    "user_id": user_id,
                    "user_name": user_name
                }

            playing_rooms.append({
                "name": room_name,
                "locked_seats": locked_seats
            })

        for room_data in playing_rooms:
            room_name = room_data["name"]
            # 创建游戏中房间
            room_widget = self.create_playing_room(room_name, 1)

            # 锁定所有座位（游戏中房间必须满员）
            if room_widget:
                for seat_index, user_info in room_data["locked_seats"].items():
                    room_widget.lock_seat(seat_index, user_info)

    # 重置调整UI大小及位置
    def resize_ui(self):
        self.logger.debug('开始调整UI大小')
        self.setMinimumSize(int(1500 * self.scale), int(945 * self.scale))
        self.setMaximumSize(int(1500 * self.scale), int(945 * self.scale))
        MainWindow.center(self)  # 调用后面定义的center方法在屏幕居中
        self.left_stack.resize(int(945 * self.scale), int(945 * self.scale))

        # 调整大厅页面标题区域的背景图片
        if hasattr(self, 'hall'):
            scaled_height = int(180 * self.scale)

            # 查找并调整标题区域
            for child in self.hall.findChildren(QWidget):
                if child.parent() == self.hall:
                    # 调整标题区域容器大小
                    child.setFixedHeight(scaled_height)

                    # 调整背景图片QLabel和文字QLabel
                    for label in child.findChildren(QLabel):
                        if label.pixmap() and not label.pixmap().isNull():
                            # 背景图片QLabel - 现在使用布局管理器，不需要手动设置几何位置
                            pass
                        elif label.text() == "游戏大厅":
                            # 标题文字QLabel
                            label.setGeometry(int(50 * self.scale), int(30 * self.scale),
                                            int(400 * self.scale), int(40 * self.scale))
                            # 调整字体大小
                            scaled_font_size = int(33 * self.scale)
                            if scaled_font_size < 25:
                                scaled_font_size = 25
                            label.setStyleSheet(f"font-size: {scaled_font_size}px; font-weight: Bold; color: #aaaaaa; background-color: transparent;")
                        elif label.text() == "欢迎加入超级四国大战":
                            # 副标题文字QLabel
                            label.setGeometry(int(50 * self.scale), int(80 * self.scale),
                                            int(400 * self.scale), int(40 * self.scale))
                            # 调整字体大小
                            scaled_font_size = int(13 * self.scale)
                            if scaled_font_size < 10:
                                scaled_font_size = 10
                            label.setStyleSheet(f"font-size: {scaled_font_size}px; color: #cccccc; background-color: transparent;")
                    break

        self.scene.setSceneRect(0, 0, int(945 * self.scale), int(945 * self.scale))
        # 给场景设置位图做为底图并缩放合适
        bg = QPixmap(r'./client/image/bg.png').scaled(int(945 * self.scale), int(945 * self.scale))
        self.scene.setBackgroundBrush(QBrush(bg))
        # 将矢量棋盘图缩放到合适大小
        wh = self.scene.background_svg.boundingRect()
        self.scene.background_svg.setScale(945 / wh.width() * self.scale)
        self.view.setFixedSize(945 * self.scale, 945 * self.scale)

        # 设置右侧标签页的大小和位置
        self.right_tabs.setFixedSize(int(420 * self.scale), int(945 * self.scale))
        self.right_tabs.move(int(965 * self.scale), 5)

        self.user_frame.setFixedSize(int(100 * self.scale), int(100 * self.scale))
        self.user_frame.move(int(1090 * self.scale), 5)

        # 设置房间区域大小（在房间信息标签页中）- 只有当房间标签页存在时才设置
        if hasattr(self, 'room') and self.room:
            self.room.setFixedSize(int(210 * self.scale), int(210 * self.scale))
        if hasattr(self, 'table') and self.table:
            self.table.setFixedSize(int(63 * self.scale), int(63 * self.scale))
            pic = QPixmap("./client/image/table.png").scaled(self.table.size(), aspectMode=Qt.AspectRatioMode.KeepAspectRatio)  # 图片自适应
            self.table.setPixmap(pic)
        # 座位缩放处理
        for seat in self.seats:
            seat.setFixedSize(int(63 * self.scale), int(63 * self.scale))
        self.logger.info(f'UI调整完成，缩放系数: {self.scale}')


    # 居中窗口的方法
    @staticmethod
    def center(win):
        # 获取主屏幕对象
        screen = QApplication.primaryScreen()
        # 获取屏幕几何信息
        screen_geometry = screen.geometry()
        # 计算窗口居中位置
        new_x = int((screen_geometry.width() - win.geometry().width()) / 2 - 6)
        new_y = int((screen_geometry.height() - win.geometry().height()) / 2 - 40)
        win.move(new_x, new_y)

    def closeEvent(self, event):
        """重写closeEvent方法，处理窗口关闭事件"""
        self.logger.info('捕获到窗口关闭事件')
        # 弹出确认对话框
        reply = QMessageBox.question(
            self,
            '确认退出',
            '确定要退出游戏吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.logger.info('用户确认退出，开始程序关闭流程')
            self.login_widget_on = False
            self.login_widget.close()
            self.logger.info('主窗口关闭事件已接受')
            event.accept()
        else:
            self.logger.info('用户取消退出')
            event.ignore()  # 忽略关闭事件


class Seat(QLabel):
    def __init__(self, seat_code):
        super().__init__()
        self.seat_code = seat_code
        self.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        self.setLineWidth(1)
        self.setMidLineWidth(0)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFont(QFont("微软雅黑", 10))
        self.setText("空座位")
        self.setEnabled(True)


class GameScene(QGraphicsScene):  # 自定义QGraphicsScene类
    def __init__(self, logger, signal, background_svg: str = ''):
        super().__init__()
        self.logger = logger
        self.signal = signal
        self.logger.info('初始化棋盘场景')
        # 加载棋盘背景图
        self.background_svg = QGraphicsSvgItem(background_svg)
        self.addItem(self.background_svg)
        self.active_flag = True
        self.logger.info('棋盘场景初始化完成')

    def mousePressEvent(self, e):  # 重写场景实例的鼠标按键事件
        # self.update()
        super().mousePressEvent(e)  # 如果不希望鼠标事件被拦截，而导致原本的点击行为被忽略，要调用父类的方法使点击产生原有效果
        scene_pos = e.scenePos()  # 如果重写view中的鼠标事件，必须将view坐标转换成场景坐标：view.mapToScene(event.pos)
        if self.active_flag:
            if e.button() == Qt.MouseButton.LeftButton:
                self.signal.emit('l', scene_pos)  # 将取得的场景点击坐标用信号发送给槽函数
            elif e.button() == Qt.MouseButton.RightButton:
                self.signal.emit('r', scene_pos)

    def freeze(self):
        """冻结棋盘，禁止交互"""
        self.logger.debug('冻结棋盘')
        self.active_flag = False

    def activate(self):
        """激活棋盘，允许交互"""
        self.logger.debug('激活棋盘')
        self.active_flag = True
