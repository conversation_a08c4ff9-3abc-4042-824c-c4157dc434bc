"""
管理员业务逻辑核心模块
"""

import asyncio
from typing import Dict, Any, Optional, Callable
from .database import DatabaseManager


class AdminCore:
    """管理员业务逻辑核心
    
    负责处理所有管理员相关的业务逻辑：
    - 管理员命令处理
    - 系统统计信息
    - 用户管理
    - 数据备份
    """
    
    def __init__(self, format_data: Callable, db_manager: DatabaseManager, logger):
        """初始化管理员核心模块
        
        Args:
            db_manager: 数据库管理器
            logger: 日志记录器
        """
        self.format_data = format_data
        self.db_manager = db_manager
        self.logger = logger
        
        # 管理员会话缓存
        self.admin_sessions = {}  # {session_id: admin_info}
        
    async def initialize(self):
        """初始化管理员核心模块"""
        self.logger.info("管理员核心模块初始化完成")
    
    async def cleanup(self):
        """清理资源"""
        self.admin_sessions.clear()
        self.logger.info("管理员核心模块资源清理完成")
    
    # ==================== 消息处理器 ====================
    
    async def handle_admin_command(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理管理员命令消息
        
        这是从原data_center.py迁移过来的管理员命令处理逻辑
        """
        try:
            self.logger.info(f"收到管理员命令: {message}")
            
            # 获取命令数据
            data = message.get('data', {})
            command = data.get('command', 'unknown')
            
            # 处理不同的管理员命令
            if command == 'status':
                result = await self._handle_status_command(data)
            elif command == 'backup':
                result = await self._handle_backup_command(data)
            elif command == 'user_list':
                result = await self._handle_user_list_command(data)
            elif command == 'shutdown':
                result = await self._handle_shutdown_command(data)
            else:
                result = {
                    'status': 'error',
                    'message': f"未知的管理员命令: {command}",
                    'available_commands': ['status', 'backup', 'user_list', 'shutdown']
                }
            
            self.logger.info(f"处理管理员命令: {command} -> {result['status']}")
            
            # 构造admin_response响应
            response = {
                'type': 'admin_response',
                'data': {
                    'command': command,
                    'result': result.get('message', f"管理员命令 '{command}' 已处理"),
                    'status': result.get('status', 'success'),
                    'timestamp': asyncio.get_event_loop().time(),
                    'details': result.get('details', {})
                },
                'socket_id': message.get('socket_id')  # 添加socket_id用于响应路由
            }
            
            return response
            
        except Exception as e:
            self.logger.error(f"处理管理员命令异常: {e}")
            return {
                'type': 'admin_response',
                'data': {
                    'command': message.get('data', {}).get('command', 'unknown'),
                    'result': '管理员命令处理失败',
                    'status': 'error',
                    'error': str(e),
                    'timestamp': asyncio.get_event_loop().time()
                }
            }
    
    async def handle_get_stats(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理获取系统统计信息"""
        try:
            # 获取数据库统计信息
            db_status = await self.db_manager.test_connection()
            
            # TODO: 从其他模块获取统计信息
            stats = {
                'database_status': db_status,
                'server_uptime': asyncio.get_event_loop().time(),
                'total_users': 0,  # TODO: 从数据库获取
                'active_connections': 0,  # TODO: 从连接管理器获取
                'active_rooms': 0,  # TODO: 从游戏核心获取
            }
            
            return {
                'type': 'admin_get_stats_response',
                'data': {
                    'success': True,
                    'stats': stats
                },
                'socket_id': message.get('socket_id')
            }
            
        except Exception as e:
            self.logger.error(f"处理获取统计信息异常: {e}")
            return {
                'type': 'admin_get_stats_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id')
            }
    
    async def handle_manage_user(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理用户管理"""
        # TODO: 实现用户管理逻辑
        return {
            'type': 'admin_manage_user_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    async def handle_backup_data(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理数据备份"""
        try:
            # 执行数据库备份
            backup_result = await self.db_manager.backup_database()
            
            return {
                'type': 'admin_backup_data_response',
                'data': {
                    'success': backup_result['success'],
                    'message': backup_result.get('message', ''),
                    'backup_path': backup_result.get('backup_path', ''),
                    'error': backup_result.get('error', '')
                },
                'socket_id': message.get('socket_id')
            }
            
        except Exception as e:
            self.logger.error(f"处理数据备份异常: {e}")
            return {
                'type': 'admin_backup_data_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id')
            }
    
    # ==================== 私有命令处理方法 ====================
    
    async def _handle_status_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理状态查询命令"""
        try:
            # 获取系统状态信息
            db_status = await self.db_manager.test_connection()
            
            status_info = {
                'server_status': 'running',
                'database_status': db_status,
                'uptime': asyncio.get_event_loop().time(),
                'memory_usage': 'N/A',  # TODO: 实现内存使用统计
                'active_connections': 0,  # TODO: 从连接管理器获取
            }
            
            return {
                'status': 'success',
                'message': '系统状态正常',
                'details': status_info
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'获取系统状态失败: {e}'
            }
    
    async def _handle_backup_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理备份命令"""
        try:
            backup_result = await self.db_manager.backup_database()
            
            if backup_result['success']:
                return {
                    'status': 'success',
                    'message': '数据备份完成',
                    'details': {
                        'backup_path': backup_result['backup_path']
                    }
                }
            else:
                return {
                    'status': 'error',
                    'message': f"数据备份失败: {backup_result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'执行备份命令失败: {e}'
            }
    
    async def _handle_user_list_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户列表命令"""
        try:
            # TODO: 从数据库获取用户列表
            # 暂时返回模拟数据
            user_list = []
            
            return {
                'status': 'success',
                'message': f'当前共有 {len(user_list)} 个用户',
                'details': {
                    'user_count': len(user_list),
                    'users': user_list
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'获取用户列表失败: {e}'
            }
    
    async def _handle_shutdown_command(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理关闭服务器命令"""
        # 注意：实际的关闭逻辑应该由主程序处理
        # 这里只是返回确认信息
        return {
            'status': 'success',
            'message': '服务器关闭命令已接收，请通过管理员控制台确认关闭',
            'details': {
                'warning': '此命令需要管理员控制台确认'
            }
        }
    
    # ==================== 辅助方法 ====================
    
    def is_admin_authenticated(self, session_id: str) -> bool:
        """检查管理员是否已认证"""
        return session_id in self.admin_sessions
    
    def add_admin_session(self, session_id: str, admin_info: Dict[str, Any]):
        """添加管理员会话"""
        self.admin_sessions[session_id] = admin_info
        self.logger.info(f"管理员会话已创建: {session_id}")
    
    def remove_admin_session(self, session_id: str):
        """移除管理员会话"""
        if session_id in self.admin_sessions:
            self.admin_sessions.pop(session_id)
            self.logger.info(f"管理员会话已移除: {session_id}")

    # ==================== 系统配置管理 ====================

    async def get_invitation_code(self) -> str:
        """获取当前邀请码

        Returns:
            str: 当前有效的邀请码
        """
        try:
            code = await self.db_manager.get_system_config('invitation_code')
            if not code:
                self.logger.warning("数据库中未找到邀请码配置")
                return 'Welcome24inWar'  # 默认邀请码
            return code
        except Exception as e:
            self.logger.error(f"获取邀请码失败: {e}")
            return 'Welcome24inWar'

    async def set_invitation_code(self, new_code: str) -> Dict[str, Any]:
        """设置新的邀请码

        Args:
            new_code: 新的邀请码

        Returns:
            Dict: 操作结果 {'success': bool, 'message': str}
        """
        try:
            # 验证邀请码格式
            if not new_code or not new_code.strip():
                return {
                    'success': False,
                    'message': '邀请码不能为空'
                }

            new_code = new_code.strip()

            # 长度限制
            if len(new_code) < 3:
                return {
                    'success': False,
                    'message': '邀请码长度不能少于3个字符'
                }

            if len(new_code) > 50:
                return {
                    'success': False,
                    'message': '邀请码长度不能超过50个字符'
                }

            # 字符限制（只允许字母、数字、下划线）
            import re
            if not re.match(r'^[a-zA-Z0-9_]+$', new_code):
                return {
                    'success': False,
                    'message': '邀请码只能包含字母、数字和下划线'
                }

            # 保存到数据库
            success = await self.db_manager.set_system_config(
                'invitation_code',
                new_code,
                'string',
                '用户注册邀请码'
            )

            if success:
                self.logger.info(f"邀请码已更新: {new_code}")
                return {
                    'success': True,
                    'message': f'邀请码已成功更新为: {new_code}'
                }
            else:
                return {
                    'success': False,
                    'message': '数据库更新失败'
                }

        except Exception as e:
            self.logger.error(f"设置邀请码失败: {e}")
            return {
                'success': False,
                'message': f'设置邀请码时发生错误: {str(e)}'
            }

    async def get_system_configs(self) -> Dict[str, Any]:
        """获取所有系统配置

        Returns:
            Dict: 系统配置字典
        """
        try:
            configs = await self.db_manager.get_all_system_configs()
            self.logger.info(f"获取系统配置成功，共{len(configs)}项")
            return {
                'success': True,
                'configs': configs
            }
        except Exception as e:
            self.logger.error(f"获取系统配置失败: {e}")
            return {
                'success': False,
                'message': f'获取系统配置失败: {str(e)}'
            }

    async def set_system_config(self, key: str, value: str, config_type: str = 'string', description: str = '') -> Dict[str, Any]:
        """设置系统配置

        Args:
            key: 配置键名
            value: 配置值
            config_type: 配置类型
            description: 配置描述

        Returns:
            Dict: 操作结果
        """
        try:
            success = await self.db_manager.set_system_config(key, value, config_type, description)

            if success:
                self.logger.info(f"系统配置已更新: {key} = {value}")
                return {
                    'success': True,
                    'message': f'配置 {key} 已成功更新'
                }
            else:
                return {
                    'success': False,
                    'message': '数据库更新失败'
                }

        except Exception as e:
            self.logger.error(f"设置系统配置失败 ({key}): {e}")
            return {
                'success': False,
                'message': f'设置配置时发生错误: {str(e)}'
            }
