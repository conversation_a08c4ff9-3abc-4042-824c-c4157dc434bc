"""
增强版数据中心 - 集成aiosqlite数据库支持
"""

import asyncio
from typing import Dict, Any, Callable, Optional
from .database import DatabaseManager
from .user_core import UserCore
from .game_core import GameCore
from .admin_core import AdminCore


class DataCenter:
    """数据中心 - 消息总线的核心处理器
    
    职责：
    1. 从pending_queue接收消息
    2. 根据消息类型分发到对应的业务逻辑模块
    3. 将处理结果发送到response_queue
    4. 管理数据库连接和业务模块
    5. 提供统一的数据访问接口
    """
    
    def __init__(self, logger, pending_queue, response_queue, db_path='server/data/game.db'):
        """初始化数据中心
        
        Args:
            logger: 日志记录器
            pending_queue: 待处理消息队列
            response_queue: 响应消息队列
            db_path: 数据库文件路径
        """
        self.logger = logger
        self.pending_queue = pending_queue
        self.response_queue = response_queue
        self.is_running = False
        
        # 初始化数据库管理器
        self.db_manager = DatabaseManager(db_path, logger)
        
        # 初始化业务逻辑模块
        self.user_core = UserCore(self.format_data, self.db_manager, logger)
        self.game_core = GameCore(self.format_data, self.db_manager, logger)
        self.admin_core = AdminCore(self.format_data, self.db_manager, logger)
        
        # 消息处理器注册表
        self.message_handlers: Dict[str, Callable] = {
            
            # 用户相关消息
            'user_register': self.user_core.handle_user_register,
            'user_login': self.user_core.handle_user_login,
            'user_logout': self.user_core.handle_user_logout,
            'get_user_profile': self.user_core.handle_get_user_profile,
            'update_user_profile': self.user_core.handle_update_user_profile,
            'get_user_ranking': self.user_core.handle_get_user_ranking,
            
            # 游戏相关消息
            'create_room': self.game_core.handle_create_room,
            'join_room': self.game_core.handle_join_room,
            'leave_room': self.game_core.handle_leave_room,
            'start_game': self.game_core.handle_start_game,
            'game_action': self.game_core.handle_game_action,
            'end_game': self.game_core.handle_end_game,
            'get_game_history': self.game_core.handle_get_game_history,
            
            # 管理员相关消息
            'admin_command': self.admin_core.handle_admin_command,
            'admin_get_stats': self.admin_core.handle_get_stats,
            'admin_manage_user': self.admin_core.handle_manage_user,
            'admin_backup_data': self.admin_core.handle_backup_data,
        }
        
        self.logger.info("数据中心初始化完成")
    
    async def start(self):
        """启动数据中心"""
        if self.is_running:
            self.logger.warning("数据中心已在运行")
            return
        
        try:
            # 初始化数据库
            await self.db_manager.initialize()
            self.logger.info("数据库初始化完成")
            
            # 启动业务模块
            await self.user_core.initialize()
            await self.game_core.initialize()
            await self.admin_core.initialize()
            self.logger.info("业务模块初始化完成")
            
            self.is_running = True
            self.logger.info("数据中心启动")
            
            # 开始消息处理循环
            await self._message_receiving_loop()
            
        except Exception as e:
            self.logger.error(f"数据中心启动异常: {e}")
            raise
        finally:
            self.is_running = False
            await self._cleanup()
            self.logger.info("数据中心已停止")
    
    async def stop(self):
        """停止数据中心"""
        self.logger.info("正在停止数据中心...")
        self.is_running = False
    
    async def _cleanup(self):
        """清理资源"""
        try:
            # 关闭业务模块
            await self.user_core.cleanup()
            await self.game_core.cleanup()
            await self.admin_core.cleanup()
            
            # 关闭数据库连接
            await self.db_manager.cleanup()
            
            self.logger.info("数据中心资源清理完成")
        except Exception as e:
            self.logger.error(f"数据中心清理异常: {e}")
    
    async def _message_receiving_loop(self):
        """消息处理主循环"""
        self.logger.info("开始消息处理循环")
        
        while self.is_running:
            try:
                # 使用 asyncio.wait 优雅地处理队列获取和状态检查
                get_task = asyncio.create_task(self.pending_queue.get())
                sleep_task = asyncio.create_task(asyncio.sleep(0.02))  # 优化：从100ms降低到20ms
                
                try:
                    done, _ = await asyncio.wait(
                        [get_task, sleep_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )
                    
                    if get_task in done:
                        # 有消息到达，获取并处理
                        message = await get_task
                        sleep_task.cancel()
                        
                        # 性能日志
                        message_type = message.get('type', 'unknown')
                        socket_id = message.get('socket_id', '')
                        self.logger.timing("DC_PROC", socket_id, message_type)
                        
                        await self._process_message(message)
                    else:
                        # 超时，取消获取任务，继续循环检查is_running状态
                        get_task.cancel()
                
                except asyncio.CancelledError:
                    # 清理未完成的任务
                    if not get_task.done():
                        get_task.cancel()
                    if not sleep_task.done():
                        sleep_task.cancel()
                    break
                    
            except Exception as e:
                self.logger.error(f"消息处理循环异常: {e}")
                await asyncio.sleep(1)  # 避免快速重试
    
    async def _process_message(self, message: Dict[str, Any]):
        """处理单个消息"""
        try:
            m_type = message.get('type', '')
            m_data = message.get('data', {})
            m_socket_id = message.get('socket_id', '')
            m_user_id = message.get('user_id', None)
            m_user_name = message.get('user_name', '')
            m_room_name = message.get('room_name', '')
            m_list = [m_type, m_data, m_socket_id, m_user_id, m_user_name, m_room_name]
            
            # 查找对应的处理器进行处理，传递的是解包出来的业务数据
            response = await self.message_handlers.get(m_type, self._handle_default_message)(m_list)
            
            # 如果有响应，直接发送到response_queue（格式已在业务模块中保证）
            if response:
                await self.response_queue.put(response)
                self.logger.info(f"✅ [DataCenter] {m_type}消息处理完成，已发送{response['type']}响应")
                    
        except Exception as e:
            self.logger.error(f"处理消息时发生错误: {e}, 消息: {message}")
            
            # 发送错误响应
            error_response = {
                'type': 'error_response',
                'data': {
                    'error': str(e),
                    'original_type': message.get('type', 'unknown')
                },
                'socket_id': message.get('socket_id'),
                'user_id': message.get('user_id'),
                'user_name': message.get('user_name'),
                'room_id': message.get('room_id'),
                'broadcast': 0
            }
            
            try:
                await self.response_queue.put(error_response)
            except Exception as queue_error:
                self.logger.error(f"发送错误响应失败: {queue_error}")
    
     
    async def _handle_default_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """默认的消息处理器"""
        message_type = message.get('type', 'unknown')
        self.logger.warning(f"未找到消息类型 '{message_type}' 的处理器")
        
        return {
            'type': 'error_response',
            'data': {
                'error': f"未知的消息类型: {message_type}",
                'supported_types': list(self.message_handlers.keys())
            },
            'socket_id': message.get('socket_id'),
            'user_id': message.get('user_id'),
            'user_name': message.get('user_name'),
            'room_id': message.get('room_id'),
            'broadcast': 0
        }
    
    def format_data(self, m_type, m_data, m_socket_id, m_user_id=None, m_user_name='', m_room_name=''):
        """格式化数据"""
        message = {
                'type': m_type,
                'data': m_data,
                'socket_id': m_socket_id,
                'user_id': m_user_id,
                'user_name': m_user_name,
                'room_name': m_room_name
                }
        return message


# 工厂函数，用于在data_thread中创建和启动DataCenter
async def create_and_run_data_center(logger, pending_queue, response_queue, db_path='server/data/game.db'):
    """在data_thread中创建并运行数据中心"""
    data_center = DataCenter(logger, pending_queue, response_queue, db_path)
    await data_center.start()
