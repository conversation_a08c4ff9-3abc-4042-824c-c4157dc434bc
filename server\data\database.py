import aiosqlite
import asyncio
import os
import json
import shutil
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
import bcrypt
import hashlib


class DatabaseManager:
    """数据库管理器 - 提供统一的数据访问接口"""
    
    def __init__(self, db_path: str, logger):
        """初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
            logger: 日志记录器
        """
        self.db_path = db_path
        self.logger = logger
        self.backup_dir = os.path.join(os.path.dirname(db_path), 'backup')
        
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 连接池配置
        self._connection_lock = asyncio.Lock()
        
    async def initialize(self):
        """初始化数据库表结构"""
        async with aiosqlite.connect(self.db_path) as db:
            # 启用外键约束
            await db.execute('PRAGMA foreign_keys = ON')
            
            # 创建用户表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    last_login TIMESTAMP,
                    total_score INTEGER DEFAULT 0,
                    games_played INTEGER DEFAULT 0,
                    games_won INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    user_level INTEGER DEFAULT 1,
                    experience_points INTEGER DEFAULT 0
                )
            ''')
            
            # 创建游戏会话表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS game_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_code TEXT UNIQUE NOT NULL,
                    players TEXT NOT NULL,  -- JSON格式存储玩家列表
                    game_data TEXT,  -- JSON格式存储游戏状态数据
                    start_time TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    end_time TIMESTAMP,
                    winner_team INTEGER,
                    game_result TEXT,  -- JSON格式存储游戏结果
                    session_status TEXT DEFAULT 'active'
                )
            ''')
            
            # 创建用户游戏统计表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS user_game_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_id INTEGER NOT NULL,
                    score INTEGER DEFAULT 0,
                    rank INTEGER,
                    team_id INTEGER,
                    actions_count INTEGER DEFAULT 0,
                    play_time INTEGER DEFAULT 0,  -- 游戏时长（秒）
                    achievements TEXT,  -- JSON格式存储成就
                    created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (session_id) REFERENCES game_sessions(id)
                )
            ''')
            
            # 创建系统配置表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT NOT NULL,
                    config_type TEXT DEFAULT 'string',
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime'))
                )
            ''')
            
            # 创建索引
            await db.execute('CREATE INDEX IF NOT EXISTS idx_username ON users(username)')
            await db.execute('CREATE INDEX IF NOT EXISTS idx_user_score ON users(total_score DESC)')
            await db.execute('CREATE INDEX IF NOT EXISTS idx_session_code ON game_sessions(session_code)')
            await db.execute('CREATE INDEX IF NOT EXISTS idx_user_stats ON user_game_stats(user_id, session_id)')
            
            await db.commit()
            
        self.logger.info("数据库表结构初始化完成")
        
        # 插入默认系统配置
        await self._insert_default_config()
    
    async def _insert_default_config(self):
        """插入默认系统配置"""
        default_configs = [
            ('server_version', '1.0.0', 'string', '服务器版本'),
            ('max_concurrent_games', '10', 'integer', '最大并发游戏数'),
            ('default_game_timeout', '1800', 'integer', '默认游戏超时时间（秒）'),
            ('user_registration_enabled', 'true', 'boolean', '是否允许用户注册'),
            ('maintenance_mode', 'false', 'boolean', '维护模式'),
            ('invitation_code', 'Welcome24inWar', 'string', '用户注册邀请码'),
        ]
        
        async with aiosqlite.connect(self.db_path) as db:
            for key, value, config_type, description in default_configs:
                await db.execute('''
                    INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description)
                    VALUES (?, ?, ?, ?)
                ''', (key, value, config_type, description))
            await db.commit()
    
    async def test_connection(self) -> str:
        """测试数据库连接"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('SELECT COUNT(*) FROM users') as cursor:
                    result = await cursor.fetchone()
                    user_count = result[0] if result else 0
                return f"数据库连接正常，当前用户数: {user_count}"
        except Exception as e:
            return f"数据库连接失败: {e}"
    
    # ==================== 用户相关操作 ====================
    
    async def authenticate_user(self, username: str, password: str, is_password_hashed: bool = True) -> Optional[Dict[str, Any]]:
        """用户认证

        Args:
            username: 用户名
            password: 密码（明文或已哈希）
            is_password_hashed: 密码是否已经哈希处理
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT id, username, password_hash, total_score, games_played, games_won, user_level
                    FROM users
                    WHERE username = ? AND is_active = 1
                ''', (username,)) as cursor:
                    user_row = await cursor.fetchone()

                    if not user_row:
                        return None

                    # 验证密码
                    stored_hash = user_row[2]

                    if is_password_hashed:
                        # 客户端发送SHA256哈希，服务器端使用bcrypt验证SHA256哈希
                        password_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                        self.logger.debug(f"SHA256哈希密码验证: {username} -> {'成功' if password_valid else '失败'}")
                    else:
                        # 明文密码，使用bcrypt验证
                        password_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                        self.logger.debug(f"明文密码验证: {username} -> {'成功' if password_valid else '失败'}")

                    if password_valid:
                        # 更新最后登录时间
                        await db.execute('''
                            UPDATE users SET last_login = datetime('now', 'localtime') WHERE id = ?
                        ''', (user_row[0],))
                        await db.commit()
                        
                        return {
                            'id': user_row[0],
                            'username': user_row[1],
                            'total_score': user_row[3],
                            'games_played': user_row[4],
                            'games_won': user_row[5],
                            'user_level': user_row[6]
                        }
                    
                    return None
                    
        except Exception as e:
            self.logger.error(f"用户认证失败: {e}")
            return None
    
    async def get_user_ranking(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户排行榜"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT username, total_score, games_played, games_won, user_level
                    FROM users 
                    WHERE is_active = 1
                    ORDER BY total_score DESC, games_won DESC
                    LIMIT ?
                ''', (limit,)) as cursor:
                    rows = await cursor.fetchall()
                    
                    return [
                        {
                            'rank': idx + 1,
                            'username': row[0],
                            'total_score': row[1],
                            'games_played': row[2],
                            'games_won': row[3],
                            'user_level': row[4],
                            'win_rate': round(row[3] / row[2] * 100, 1) if row[2] > 0 else 0
                        }
                        for idx, row in enumerate(rows)
                    ]
                    
        except Exception as e:
            self.logger.error(f"获取排行榜失败: {e}")
            return []
    
    # ==================== 游戏相关操作 ====================
    
    async def create_game_session(self, session_code: str, players: List[str]) -> Dict[str, Any]:
        """创建游戏会话 - 只设置必需字段"""
        try:
            session_data = {
                'session_code': session_code,
                'players': json.dumps(players)
                # 其他字段使用默认值：
                # start_time: DEFAULT (datetime('now', 'localtime'))
                # session_status: DEFAULT 'active'
            }
            
            result = await self.insert_or_ignore(
                'game_sessions',
                session_data,
                returning_fields=['id', 'session_code', 'start_time', 'session_status']
            )
            
            if result['success'] and result['inserted']:
                return {
                    'success': True,
                    'session_id': result['last_insert_id'],
                    'session_data': result['returned_data']
                }
            else:
                return {'success': False, 'error': '会话代码已存在'}
                
        except Exception as e:
            self.logger.error(f"创建游戏会话失败: {e}")
            return {'success': False, 'error': str(e)}
    
    # ==================== 备份和维护 ====================
    
    async def backup_database(self) -> Dict[str, Any]:
        """备份数据库"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = os.path.join(self.backup_dir, f'game_backup_{timestamp}.db')
            
            # 复制数据库文件
            shutil.copy2(self.db_path, backup_path)
            
            # 清理旧备份（保留最近10个）
            await self._cleanup_old_backups()
            
            return {
                'success': True,
                'backup_path': backup_path,
                'message': '数据库备份成功'
            }
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return {
                'success': False,
                'error': f'备份失败: {e}'
            }
    
    async def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧备份文件"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith('game_backup_') and f.endswith('.db')]
            backup_files.sort(reverse=True)  # 按时间倒序
            
            # 删除多余的备份文件
            for old_backup in backup_files[keep_count:]:
                old_path = os.path.join(self.backup_dir, old_backup)
                os.remove(old_path)
                self.logger.info(f"删除旧备份文件: {old_backup}")
                
        except Exception as e:
            self.logger.error(f"清理旧备份失败: {e}")

    # ==================== 系统配置相关操作 ====================

    async def get_system_config(self, config_key: str) -> Optional[str]:
        """获取系统配置值

        Args:
            config_key: 配置键名

        Returns:
            str: 配置值，如果不存在返回None
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT config_value FROM system_config WHERE config_key = ?
                ''', (config_key,)) as cursor:
                    result = await cursor.fetchone()
                    return result[0] if result else None

        except Exception as e:
            self.logger.error(f"获取系统配置失败 ({config_key}): {e}")
            return None

    async def set_system_config(self, config_key: str, config_value: str, config_type: str = 'string', description: str = '') -> bool:
        """设置系统配置值

        Args:
            config_key: 配置键名
            config_value: 配置值
            config_type: 配置类型 (string/integer/boolean)
            description: 配置描述

        Returns:
            bool: 设置成功返回True
        """
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute('''
                    INSERT OR REPLACE INTO system_config
                    (config_key, config_value, config_type, description, updated_at)
                    VALUES (?, ?, ?, ?, datetime('now', 'localtime'))
                ''', (config_key, config_value, config_type, description))
                await db.commit()

                self.logger.info(f"系统配置已更新: {config_key} = {config_value}")
                return True

        except Exception as e:
            self.logger.error(f"设置系统配置失败 ({config_key}): {e}")
            return False

    async def get_all_system_configs(self) -> Dict[str, Any]:
        """获取所有系统配置

        Returns:
            Dict: 配置字典 {key: {'value': str, 'type': str, 'description': str}}
        """
        try:
            configs = {}
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT config_key, config_value, config_type, description
                    FROM system_config ORDER BY config_key
                ''') as cursor:
                    async for row in cursor:
                        configs[row[0]] = {
                            'value': row[1],
                            'type': row[2],
                            'description': row[3]
                        }
            return configs

        except Exception as e:
            self.logger.error(f"获取所有系统配置失败: {e}")
            return {}

    async def cleanup(self):
        """清理资源"""
        # aiosqlite会自动管理连接，这里主要是日志记录
        self.logger.info("数据库管理器资源清理完成")

    # ==================== 基础接口 ====================
    
    async def insert_or_ignore(self, table_name: str, data: Dict[str, Any], 
                              returning_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """通用INSERT OR IGNORE接口 - ORM风格
        
        Args:
            table_name: 表名
            data: 要插入的数据字典 {字段名: 值}
            returning_fields: 需要返回的字段列表，如 ['id', 'created_at']
            
        Returns:
            Dict: 操作结果
            {
                'success': bool,           # 操作是否成功
                'inserted': bool,          # 是否实际插入了新记录
                'last_insert_id': int,     # 最后插入的ID（如果有）
                'returned_data': dict,     # 返回的字段数据（如果指定了returning_fields）
                'error': str               # 错误信息（如果失败）
            }
        """
        try:
            # 验证输入参数
            if not table_name or not isinstance(table_name, str):
                return {'success': False, 'error': '表名不能为空且必须是字符串'}
            
            if not data or not isinstance(data, dict):
                return {'success': False, 'error': '数据不能为空且必须是字典格式'}
            
            # 过滤掉值为None的字段
            filtered_data = {k: v for k, v in data.items() if v is not None}
            
            if not filtered_data:
                return {'success': False, 'error': '过滤后的数据为空'}
            
            # 构建SQL语句
            columns = list(filtered_data.keys())
            placeholders = ['?' for _ in columns]
            values = list(filtered_data.values())
            
            sql = f'''
                INSERT OR IGNORE INTO {table_name} 
                ({', '.join(columns)}) 
                VALUES ({', '.join(placeholders)})
            '''
            
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(sql, values)
                
                # 判定是否实际插入：rowcount > 0 表示插入了新记录，= 0 表示记录已存在被忽略
                inserted = cursor.rowcount > 0
                
                # 获取新插入记录的ID（仅在实际插入时有效）
                last_insert_id = cursor.lastrowid if inserted else None
                
                result = {
                    'success': True,        # SQL执行成功（无异常）
                    'inserted': inserted,   # 是否实际插入了新记录
                    'last_insert_id': last_insert_id,
                    'returned_data': {}
                }
                
                # 如果指定了返回字段且实际插入了数据，查询返回指定字段
                if returning_fields and inserted and last_insert_id:
                    try:
                        return_columns = ', '.join(returning_fields)
                        return_sql = f'SELECT {return_columns} FROM {table_name} WHERE rowid = ?'
                        
                        async with db.execute(return_sql, (last_insert_id,)) as return_cursor:
                            row = await return_cursor.fetchone()
                            if row:
                                # 将查询结果转换为字典：{字段名: 值}
                                result['returned_data'] = dict(zip(returning_fields, row))
                    except Exception as e:
                        # 返回字段查询失败不影响主操作，记录警告即可
                        self.logger.warning(f"获取返回字段失败: {e}")
                
                await db.commit()
                return result
                
        except Exception as e:
            # SQL执行异常，返回失败状态
            self.logger.error(f"INSERT OR IGNORE操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'inserted': False,
                'last_insert_id': None,
                'returned_data': {},
                'error': str(e)
            }
    
    async def batch_insert_or_ignore(self, table_name: str, data_list: List[Dict[str, Any]],
                                   batch_size: int = 100) -> Dict[str, Any]:
        """批量INSERT OR IGNORE接口

        Args:
            table_name: 表名
            data_list: 要插入的数据字典列表
            batch_size: 批处理大小，默认100条

        Returns:
            Dict: 批量操作结果
            {
                'success': bool,
                'total_records': int,      # 总记录数
                'inserted_count': int,     # 实际插入的记录数
                'skipped_count': int,      # 跳过的记录数（已存在）
                'error': str
            }
        """
        try:
            if not data_list:
                return {
                    'success': True,
                    'total_records': 0,
                    'inserted_count': 0,
                    'skipped_count': 0
                }

            total_records = len(data_list)
            total_inserted = 0

            # 获取字段模板
            sample_data = {k: v for k, v in data_list[0].items() if v is not None}
            columns = list(sample_data.keys())
            placeholders = ['?' for _ in columns]

            sql = f'''
                INSERT OR IGNORE INTO {table_name}
                ({', '.join(columns)})
                VALUES ({', '.join(placeholders)})
            '''

            # 分批处理
            async with aiosqlite.connect(self.db_path) as db:
                for i in range(0, total_records, batch_size):
                    batch_data = data_list[i:i + batch_size]

                    for record in batch_data:
                        try:
                            filtered_record = {k: v for k, v in record.items() if v is not None}
                            values = [filtered_record.get(col) for col in columns]

                            cursor = await db.execute(sql, values)
                            if cursor.rowcount > 0:
                                total_inserted += 1
                        except Exception as e:
                            self.logger.warning(f"批量插入单条记录失败: {e}")

                await db.commit()

            skipped_count = total_records - total_inserted

            return {
                'success': True,
                'total_records': total_records,
                'inserted_count': total_inserted,
                'skipped_count': skipped_count
            }

        except Exception as e:
            self.logger.error(f"批量INSERT OR IGNORE操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'total_records': len(data_list) if data_list else 0,
                'inserted_count': 0,
                'skipped_count': 0,
                'error': str(e)
            }

    async def select_data(self, table_name: str, columns: List[str],
                         where_conditions: Optional[Dict[str, Any]] = None,
                         order_by: Optional[str] = None,
                         limit: Optional[int] = None,
                         offset: Optional[int] = None) -> Dict[str, Any]:
        """通用SELECT查询接口 - ORM风格

        Args:
            table_name: 表名
            columns: 需要查询的字段列表，如 ['id', 'username', 'total_score']
            where_conditions: WHERE条件字典 {字段名: 值}，支持简单的等值查询
            order_by: 排序字段，如 'total_score DESC' 或 'created_at ASC'
            limit: 限制返回记录数
            offset: 偏移量（用于分页）

        Returns:
            Dict: 查询结果
            {
                'success': bool,           # 操作是否成功
                'data': List[Dict],        # 查询结果列表，每个元素是 {字段名: 值} 的字典
                'count': int,              # 返回的记录数
                'error': str               # 错误信息（如果失败）
            }
        """
        try:
            # 验证输入参数
            if not table_name or not isinstance(table_name, str):
                return {'success': False, 'data': [], 'count': 0, 'error': '表名不能为空且必须是字符串'}

            if not columns or not isinstance(columns, list):
                return {'success': False, 'data': [], 'count': 0, 'error': '字段列表不能为空且必须是列表格式'}

            # 构建SELECT语句
            columns_str = ', '.join(columns)
            sql = f'SELECT {columns_str} FROM {table_name}'
            values = []

            # 添加WHERE条件
            if where_conditions and isinstance(where_conditions, dict):
                # 过滤掉值为None的条件
                filtered_conditions = {k: v for k, v in where_conditions.items() if v is not None}

                if filtered_conditions:
                    where_clauses = []
                    for key, value in filtered_conditions.items():
                        where_clauses.append(f'{key} = ?')
                        values.append(value)

                    sql += f' WHERE {" AND ".join(where_clauses)}'

            # 添加ORDER BY
            if order_by and isinstance(order_by, str):
                sql += f' ORDER BY {order_by}'

            # 添加LIMIT和OFFSET
            if limit and isinstance(limit, int) and limit > 0:
                sql += f' LIMIT {limit}'

                if offset and isinstance(offset, int) and offset >= 0:
                    sql += f' OFFSET {offset}'

            # 执行查询
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute(sql, values) as cursor:
                    rows = await cursor.fetchall()

                    # 将查询结果转换为字典列表
                    result_data = []
                    for row in rows:
                        row_dict = dict(zip(columns, row))
                        result_data.append(row_dict)

                    return {
                        'success': True,
                        'data': result_data,
                        'count': len(result_data)
                    }

        except Exception as e:
            self.logger.error(f"SELECT查询操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'data': [],
                'count': 0,
                'error': str(e)
            }

    async def select_one(self, table_name: str, columns: List[str],
                        where_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """查询单条记录接口 - 简化版本

        Args:
            table_name: 表名
            columns: 需要查询的字段列表
            where_conditions: WHERE条件字典，通常用于主键或唯一键查询

        Returns:
            Dict: 查询结果
            {
                'success': bool,           # 操作是否成功
                'data': Dict,              # 查询结果字典 {字段名: 值}，如果没找到则为空字典
                'found': bool,             # 是否找到记录
                'error': str               # 错误信息（如果失败）
            }
        """
        try:
            result = await self.select_data(
                table_name=table_name,
                columns=columns,
                where_conditions=where_conditions,
                limit=1
            )

            if result['success']:
                if result['count'] > 0:
                    return {
                        'success': True,
                        'data': result['data'][0],  # 返回第一条记录
                        'found': True
                    }
                else:
                    return {
                        'success': True,
                        'data': {},
                        'found': False
                    }
            else:
                return {
                    'success': False,
                    'data': {},
                    'found': False,
                    'error': result.get('error', '查询失败')
                }

        except Exception as e:
            self.logger.error(f"SELECT ONE查询操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'data': {},
                'found': False,
                'error': str(e)
            }

    async def update_data(self, table_name: str, update_data: Dict[str, Any],
                         where_conditions: Dict[str, Any],
                         returning_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """通用UPDATE接口 - ORM风格

        Args:
            table_name: 表名
            update_data: 要更新的数据字典 {字段名: 新值}
            where_conditions: WHERE条件字典 {字段名: 值}
            returning_fields: 需要返回的字段列表（更新后的值）

        Returns:
            Dict: 更新结果
            {
                'success': bool,           # 操作是否成功
                'updated': bool,           # 是否实际更新了记录
                'affected_rows': int,      # 受影响的行数
                'returned_data': List[Dict], # 返回的字段数据（如果指定了returning_fields）
                'error': str               # 错误信息（如果失败）
            }
        """
        try:
            # 验证输入参数
            if not table_name or not isinstance(table_name, str):
                return {'success': False, 'updated': False, 'affected_rows': 0, 'error': '表名不能为空且必须是字符串'}

            if not update_data or not isinstance(update_data, dict):
                return {'success': False, 'updated': False, 'affected_rows': 0, 'error': '更新数据不能为空且必须是字典格式'}

            if not where_conditions or not isinstance(where_conditions, dict):
                return {'success': False, 'updated': False, 'affected_rows': 0, 'error': 'WHERE条件不能为空且必须是字典格式'}

            # 过滤掉值为None的字段
            filtered_update_data = {k: v for k, v in update_data.items() if v is not None}
            filtered_where_conditions = {k: v for k, v in where_conditions.items() if v is not None}

            if not filtered_update_data:
                return {'success': False, 'updated': False, 'affected_rows': 0, 'error': '过滤后的更新数据为空'}

            if not filtered_where_conditions:
                return {'success': False, 'updated': False, 'affected_rows': 0, 'error': '过滤后的WHERE条件为空'}

            # 构建UPDATE语句
            set_clauses = [f'{key} = ?' for key in filtered_update_data.keys()]
            where_clauses = [f'{key} = ?' for key in filtered_where_conditions.keys()]

            sql = f'''
                UPDATE {table_name}
                SET {', '.join(set_clauses)}
                WHERE {' AND '.join(where_clauses)}
            '''

            # 准备参数值
            values = list(filtered_update_data.values()) + list(filtered_where_conditions.values())

            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(sql, values)

                # 获取受影响的行数
                affected_rows = cursor.rowcount
                updated = affected_rows > 0

                result = {
                    'success': True,
                    'updated': updated,
                    'affected_rows': affected_rows,
                    'returned_data': []
                }

                # 如果指定了返回字段且实际更新了数据，查询更新后的记录
                if returning_fields and updated:
                    try:
                        return_columns = ', '.join(returning_fields)
                        where_clauses_return = [f'{key} = ?' for key in filtered_where_conditions.keys()]
                        return_sql = f'''
                            SELECT {return_columns} FROM {table_name}
                            WHERE {' AND '.join(where_clauses_return)}
                        '''

                        async with db.execute(return_sql, list(filtered_where_conditions.values())) as return_cursor:
                            rows = await return_cursor.fetchall()
                            # 将查询结果转换为字典列表
                            for row in rows:
                                result['returned_data'].append(dict(zip(returning_fields, row)))
                    except Exception as e:
                        # 返回字段查询失败不影响主操作，记录警告即可
                        self.logger.warning(f"获取更新后字段失败: {e}")

                await db.commit()
                return result

        except Exception as e:
            self.logger.error(f"UPDATE操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'updated': False,
                'affected_rows': 0,
                'returned_data': [],
                'error': str(e)
            }

    async def insert_or_update(self, table_name: str, data: Dict[str, Any],
                              conflict_columns: List[str],
                              returning_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """INSERT OR UPDATE接口 (UPSERT) - ORM风格

        当记录不存在时插入，存在时更新。基于指定的冲突字段判断。

        Args:
            table_name: 表名
            data: 要插入或更新的数据字典 {字段名: 值}
            conflict_columns: 用于判断冲突的字段列表，如 ['username'] 或 ['id']
            returning_fields: 需要返回的字段列表

        Returns:
            Dict: 操作结果
            {
                'success': bool,           # 操作是否成功
                'inserted': bool,          # 是否插入了新记录
                'updated': bool,           # 是否更新了现有记录
                'operation': str,          # 'insert' 或 'update'
                'last_insert_id': int,     # 最后插入的ID（仅插入时有效）
                'returned_data': dict,     # 返回的字段数据
                'error': str               # 错误信息（如果失败）
            }
        """
        try:
            # 验证输入参数
            if not table_name or not isinstance(table_name, str):
                return {'success': False, 'inserted': False, 'updated': False, 'error': '表名不能为空且必须是字符串'}

            if not data or not isinstance(data, dict):
                return {'success': False, 'inserted': False, 'updated': False, 'error': '数据不能为空且必须是字典格式'}

            if not conflict_columns or not isinstance(conflict_columns, list):
                return {'success': False, 'inserted': False, 'updated': False, 'error': '冲突字段列表不能为空且必须是列表格式'}

            # 过滤掉值为None的字段
            filtered_data = {k: v for k, v in data.items() if v is not None}

            if not filtered_data:
                return {'success': False, 'inserted': False, 'updated': False, 'error': '过滤后的数据为空'}

            # 验证冲突字段是否在数据中
            for col in conflict_columns:
                if col not in filtered_data:
                    return {'success': False, 'inserted': False, 'updated': False, 'error': f'冲突字段 {col} 不在数据中'}

            # 构建INSERT OR REPLACE语句（SQLite的UPSERT语法）
            columns = list(filtered_data.keys())
            placeholders = ['?' for _ in columns]
            values = list(filtered_data.values())

            # 使用INSERT OR REPLACE（需要主键或UNIQUE约束）
            # 或者使用INSERT ... ON CONFLICT DO UPDATE（更现代的语法）

            # 先尝试查询是否存在记录
            conflict_conditions = {col: filtered_data[col] for col in conflict_columns}
            existing_check = await self.select_one(
                table_name=table_name,
                columns=['rowid'],  # 只查询rowid来检查存在性
                where_conditions=conflict_conditions
            )

            async with aiosqlite.connect(self.db_path) as db:
                if existing_check['found']:
                    # 记录存在，执行更新
                    update_data = {k: v for k, v in filtered_data.items() if k not in conflict_columns}

                    if update_data:  # 有需要更新的字段
                        set_clauses = [f'{key} = ?' for key in update_data.keys()]
                        where_clauses = [f'{key} = ?' for key in conflict_conditions.keys()]

                        sql = f'''
                            UPDATE {table_name}
                            SET {', '.join(set_clauses)}
                            WHERE {' AND '.join(where_clauses)}
                        '''

                        update_values = list(update_data.values()) + list(conflict_conditions.values())
                        cursor = await db.execute(sql, update_values)

                        result = {
                            'success': True,
                            'inserted': False,
                            'updated': cursor.rowcount > 0,
                            'operation': 'update',
                            'last_insert_id': None,
                            'returned_data': {}
                        }
                    else:
                        # 没有需要更新的字段
                        result = {
                            'success': True,
                            'inserted': False,
                            'updated': False,
                            'operation': 'no_change',
                            'last_insert_id': None,
                            'returned_data': {}
                        }
                else:
                    # 记录不存在，执行插入
                    sql = f'''
                        INSERT INTO {table_name}
                        ({', '.join(columns)})
                        VALUES ({', '.join(placeholders)})
                    '''

                    cursor = await db.execute(sql, values)

                    result = {
                        'success': True,
                        'inserted': True,
                        'updated': False,
                        'operation': 'insert',
                        'last_insert_id': cursor.lastrowid,
                        'returned_data': {}
                    }

                # 如果指定了返回字段，查询最终结果
                if returning_fields and (result['inserted'] or result['updated']):
                    try:
                        return_columns = ', '.join(returning_fields)
                        where_clauses = [f'{key} = ?' for key in conflict_conditions.keys()]
                        return_sql = f'''
                            SELECT {return_columns} FROM {table_name}
                            WHERE {' AND '.join(where_clauses)}
                        '''

                        async with db.execute(return_sql, list(conflict_conditions.values())) as return_cursor:
                            row = await return_cursor.fetchone()
                            if row:
                                result['returned_data'] = dict(zip(returning_fields, row))
                    except Exception as e:
                        self.logger.warning(f"获取返回字段失败: {e}")

                await db.commit()
                return result

        except Exception as e:
            self.logger.error(f"INSERT OR UPDATE操作失败 ({table_name}): {e}")
            return {
                'success': False,
                'inserted': False,
                'updated': False,
                'operation': 'error',
                'last_insert_id': None,
                'returned_data': {},
                'error': str(e)
            }








