"""
游戏业务逻辑核心模块
"""

import asyncio
from typing import Dict, Any, Optional, Callable
from .database import DatabaseManager


class GameCore:
    """游戏业务逻辑核心
    
    负责处理所有游戏相关的业务逻辑：
    - 游戏房间创建、加入、离开
    - 游戏会话管理
    - 游戏状态处理
    - 游戏历史记录
    """
    
    def __init__(self, format_data: Callable, db_manager: DatabaseManager, logger):
        """初始化游戏核心模块
        
        Args:
            db_manager: 数据库管理器
            logger: 日志记录器
        """
        self.format_data = format_data
        self.db_manager = db_manager
        self.logger = logger
        
        # 活跃游戏房间缓存
        self.active_rooms = {}  # {room_id: room_info}
        self.room_players = {}  # {room_id: [player_list]}
        self.player_rooms = {}  # {socket_id: room_id}
        
    async def initialize(self):
        """初始化游戏核心模块"""
        self.logger.info("游戏核心模块初始化完成")
    
    async def cleanup(self):
        """清理资源"""
        self.active_rooms.clear()
        self.room_players.clear()
        self.player_rooms.clear()
        self.logger.info("游戏核心模块资源清理完成")
    
    # ==================== 消息处理器 ====================
    
    async def handle_create_room(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理创建游戏房间"""
        try:
            data = message.get('data', {})
            room_name = data.get('room_name', '').strip()
            socket_id = message.get('socket_id')
            
            # 验证输入
            if not room_name:
                return {
                    'type': 'create_room_response',
                    'data': {
                        'success': False,
                        'error': '房间名称不能为空'
                    },
                    'socket_id': socket_id,
                    'user_id': message.get('user_id'),
                    'user_name': message.get('user_name'),
                    'room_id': None,
                    'broadcast': 0
                }
            
            # TODO: 验证用户是否已登录
            # TODO: 检查用户是否已在其他房间
            
            # 模拟创建房间（暂时不使用数据库）
            room_id = f"room_{len(self.active_rooms) + 1}"
            room_config = data.get('config', {})
            
            # 创建房间信息
            room_info = {
                'room_id': room_id,
                'room_name': room_name,
                'creator_socket': socket_id,
                'max_players': room_config.get('max_players', 4),
                'current_players': 1,
                'status': 'waiting',
                'created_time': asyncio.get_event_loop().time()
            }
            
            # 更新缓存
            self.active_rooms[room_id] = room_info
            self.room_players[room_id] = [socket_id]
            self.player_rooms[socket_id] = room_id
            
            self.logger.info(f"创建游戏房间成功: {room_name} (ID: {room_id}, 创建者: {socket_id})")
            
            return {
                'type': 'create_room_response',
                'data': {
                    'success': True,
                    'room_info': room_info,
                    'message': '房间创建成功'
                },
                'socket_id': socket_id,
                'user_id': message.get('user_id'),
                'user_name': message.get('user_name'),
                'room_id': room_id,
                'broadcast': 0
            }
            
        except Exception as e:
            self.logger.error(f"处理创建房间异常: {e}")
            return {
                'type': 'create_room_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id'),
                'user_id': message.get('user_id'),
                'user_name': message.get('user_name'),
                'room_id': None,
                'broadcast': 0
            }
    
    async def handle_join_room(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理加入游戏房间"""
        try:
            data = message.get('data', {})
            room_id = data.get('room_id', '').strip()
            socket_id = message.get('socket_id')
            
            # 验证输入
            if not room_id:
                return {
                    'type': 'join_room_response',
                    'data': {
                        'success': False,
                        'error': '房间ID不能为空'
                    },
                    'socket_id': socket_id
                }
            
            # 检查房间是否存在
            room_info = self.active_rooms.get(room_id)
            if not room_info:
                return {
                    'type': 'join_room_response',
                    'data': {
                        'success': False,
                        'error': '房间不存在'
                    },
                    'socket_id': socket_id
                }
            
            # 检查房间是否已满
            if room_info['current_players'] >= room_info['max_players']:
                return {
                    'type': 'join_room_response',
                    'data': {
                        'success': False,
                        'error': '房间已满'
                    },
                    'socket_id': socket_id
                }
            
            # 检查用户是否已在房间中
            if socket_id in self.room_players.get(room_id, []):
                return {
                    'type': 'join_room_response',
                    'data': {
                        'success': False,
                        'error': '您已在该房间中'
                    },
                    'socket_id': socket_id
                }
            
            # 加入房间
            self.room_players[room_id].append(socket_id)
            self.player_rooms[socket_id] = room_id
            room_info['current_players'] += 1
            
            self.logger.info(f"玩家加入房间: {socket_id} -> {room_id}")
            
            return {
                'type': 'join_room_response',
                'data': {
                    'success': True,
                    'room_info': room_info,
                    'player_count': len(self.room_players[room_id]),
                    'message': '成功加入房间'
                },
                'socket_id': socket_id
            }
            
        except Exception as e:
            self.logger.error(f"处理加入房间异常: {e}")
            return {
                'type': 'join_room_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id')
            }
    
    async def handle_leave_room(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理离开游戏房间"""
        try:
            socket_id = message.get('socket_id')
            room_id = self.player_rooms.get(socket_id)
            
            if not room_id:
                return {
                    'type': 'leave_room_response',
                    'data': {
                        'success': False,
                        'error': '您不在任何房间中'
                    },
                    'socket_id': socket_id
                }
            
            # 从房间中移除玩家
            if room_id in self.room_players:
                self.room_players[room_id].remove(socket_id)
                self.active_rooms[room_id]['current_players'] -= 1
            
            self.player_rooms.pop(socket_id, None)
            
            # 如果房间为空，删除房间
            if not self.room_players.get(room_id):
                self.active_rooms.pop(room_id, None)
                self.room_players.pop(room_id, None)
                self.logger.info(f"房间已删除: {room_id} (无玩家)")
            
            self.logger.info(f"玩家离开房间: {socket_id} <- {room_id}")
            
            return {
                'type': 'leave_room_response',
                'data': {
                    'success': True,
                    'message': '成功离开房间'
                },
                'socket_id': socket_id
            }
            
        except Exception as e:
            self.logger.error(f"处理离开房间异常: {e}")
            return {
                'type': 'leave_room_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id')
            }
    
    async def handle_start_game(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理开始游戏"""
        # TODO: 实现游戏开始逻辑
        return {
            'type': 'start_game_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    async def handle_game_action(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理游戏动作"""
        # TODO: 实现游戏动作处理逻辑
        return {
            'type': 'game_action_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    async def handle_end_game(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理结束游戏"""
        # TODO: 实现游戏结束逻辑
        return {
            'type': 'end_game_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    async def handle_get_game_history(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理获取游戏历史"""
        # TODO: 实现游戏历史查询逻辑
        return {
            'type': 'get_game_history_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id')
        }
    
    # ==================== 辅助方法 ====================
    
    def get_room_count(self) -> int:
        """获取活跃房间数量"""
        return len(self.active_rooms)
    
    def get_player_room(self, socket_id: str) -> Optional[str]:
        """获取玩家所在房间ID"""
        return self.player_rooms.get(socket_id)
    
    def disconnect_player(self, socket_id: str):
        """断开玩家连接（清理房间状态）"""
        room_id = self.player_rooms.get(socket_id)
        if room_id:
            # 模拟离开房间的处理
            asyncio.create_task(self.handle_leave_room({
                'socket_id': socket_id,
                'data': {}
            }))
