#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库查询接口测试脚本
演示新增的 select_data 和 select_one 接口的使用方法
"""

import asyncio
import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager


class TestLogger:
    """简单的测试日志器"""
    
    def info(self, msg):
        print(f"[INFO] {msg}")
    
    def error(self, msg):
        print(f"[ERROR] {msg}")
    
    def warning(self, msg):
        print(f"[WARNING] {msg}")
    
    def debug(self, msg):
        print(f"[DEBUG] {msg}")


async def test_select_interfaces():
    """测试查询接口"""
    
    # 创建测试数据库
    db_path = "test_select.db"
    logger = TestLogger()
    db_manager = DatabaseManager(db_path, logger)
    
    try:
        # 初始化数据库
        await db_manager.initialize()
        print("✅ 数据库初始化完成")
        
        # 1. 测试插入一些测试数据
        print("\n=== 1. 插入测试数据 ===")
        
        test_users = [
            {"username": "player1", "password_hash": "hash1", "total_score": 1500, "games_played": 10, "games_won": 7},
            {"username": "player2", "password_hash": "hash2", "total_score": 1200, "games_played": 8, "games_won": 4},
            {"username": "player3", "password_hash": "hash3", "total_score": 1800, "games_played": 15, "games_won": 12},
        ]
        
        for user_data in test_users:
            result = await db_manager.insert_or_ignore("users", user_data)
            print(f"插入用户 {user_data['username']}: {'成功' if result['inserted'] else '已存在'}")
        
        # 2. 测试 select_data 接口 - 查询所有用户
        print("\n=== 2. 测试 select_data - 查询所有用户 ===")
        
        result = await db_manager.select_data(
            table_name="users",
            columns=["id", "username", "total_score", "games_played", "games_won"]
        )
        
        if result['success']:
            print(f"查询成功，共找到 {result['count']} 条记录:")
            for user in result['data']:
                print(f"  - ID: {user['id']}, 用户名: {user['username']}, 总分: {user['total_score']}")
        else:
            print(f"查询失败: {result['error']}")
        
        # 3. 测试 select_data 接口 - 带WHERE条件查询
        print("\n=== 3. 测试 select_data - 带WHERE条件查询 ===")
        
        result = await db_manager.select_data(
            table_name="users",
            columns=["username", "total_score", "games_won"],
            where_conditions={"total_score": 1500}
        )
        
        if result['success']:
            print(f"查询总分为1500的用户，共找到 {result['count']} 条记录:")
            for user in result['data']:
                print(f"  - 用户名: {user['username']}, 总分: {user['total_score']}, 胜场: {user['games_won']}")
        else:
            print(f"查询失败: {result['error']}")
        
        # 4. 测试 select_data 接口 - 带排序和限制
        print("\n=== 4. 测试 select_data - 带排序和限制 ===")
        
        result = await db_manager.select_data(
            table_name="users",
            columns=["username", "total_score"],
            order_by="total_score DESC",
            limit=2
        )
        
        if result['success']:
            print(f"查询前2名用户（按总分排序），共找到 {result['count']} 条记录:")
            for i, user in enumerate(result['data'], 1):
                print(f"  - 第{i}名: {user['username']}, 总分: {user['total_score']}")
        else:
            print(f"查询失败: {result['error']}")
        
        # 5. 测试 select_one 接口 - 查询单个用户
        print("\n=== 5. 测试 select_one - 查询单个用户 ===")
        
        result = await db_manager.select_one(
            table_name="users",
            columns=["id", "username", "total_score", "games_played", "games_won"],
            where_conditions={"username": "player2"}
        )
        
        if result['success'] and result['found']:
            user = result['data']
            print(f"找到用户: {user['username']}")
            print(f"  - ID: {user['id']}")
            print(f"  - 总分: {user['total_score']}")
            print(f"  - 游戏场次: {user['games_played']}")
            print(f"  - 胜场: {user['games_won']}")
        elif result['success'] and not result['found']:
            print("未找到指定用户")
        else:
            print(f"查询失败: {result['error']}")
        
        # 6. 测试查询不存在的用户
        print("\n=== 6. 测试查询不存在的用户 ===")
        
        result = await db_manager.select_one(
            table_name="users",
            columns=["username", "total_score"],
            where_conditions={"username": "nonexistent_user"}
        )
        
        if result['success']:
            if result['found']:
                print(f"找到用户: {result['data']}")
            else:
                print("未找到用户（符合预期）")
        else:
            print(f"查询失败: {result['error']}")
        
        # 7. 测试系统配置查询
        print("\n=== 7. 测试系统配置查询 ===")
        
        result = await db_manager.select_data(
            table_name="system_config",
            columns=["config_key", "config_value", "config_type", "description"],
            order_by="config_key ASC"
        )
        
        if result['success']:
            print(f"系统配置，共 {result['count']} 项:")
            for config in result['data']:
                print(f"  - {config['config_key']}: {config['config_value']} ({config['config_type']})")
        else:
            print(f"查询失败: {result['error']}")
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理测试数据库文件
        if os.path.exists(db_path):
            os.remove(db_path)
            print(f"🗑️ 已清理测试数据库文件: {db_path}")


if __name__ == "__main__":
    print("🚀 开始测试数据库查询接口...")
    asyncio.run(test_select_interfaces())
