#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库更新接口测试脚本
演示新增的 update_data 和 insert_or_update 接口的使用方法
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import DatabaseManager


class TestLogger:
    """简单的测试日志器"""
    
    def info(self, msg):
        print(f"[INFO] {msg}")
    
    def error(self, msg):
        print(f"[ERROR] {msg}")
    
    def warning(self, msg):
        print(f"[WARNING] {msg}")
    
    def debug(self, msg):
        print(f"[DEBUG] {msg}")


async def test_update_interfaces():
    """测试更新接口"""
    
    # 创建测试数据库
    db_path = os.path.join(os.getcwd(), "test_update.db")
    logger = TestLogger()
    db_manager = DatabaseManager(db_path, logger)
    
    try:
        # 初始化数据库
        await db_manager.initialize()
        print("✅ 数据库初始化完成")
        
        # 1. 插入测试数据
        print("\n=== 1. 插入测试数据 ===")
        
        test_users = [
            {"username": "player1", "password_hash": "hash1", "total_score": 1000, "games_played": 5},
            {"username": "player2", "password_hash": "hash2", "total_score": 800, "games_played": 3},
        ]
        
        for user_data in test_users:
            result = await db_manager.insert_or_ignore("users", user_data)
            print(f"插入用户 {user_data['username']}: {'成功' if result['inserted'] else '已存在'}")
        
        # 查看初始数据
        users = await db_manager.select_data("users", ["id", "username", "total_score", "games_played"])
        print("初始用户数据:")
        for user in users['data']:
            print(f"  - ID: {user['id']}, 用户: {user['username']}, 总分: {user['total_score']}, 游戏数: {user['games_played']}")
        
        # 2. 测试 update_data 接口
        print("\n=== 2. 测试 update_data - 更新用户分数 ===")
        
        result = await db_manager.update_data(
            table_name="users",
            update_data={"total_score": 1500, "games_played": 8},
            where_conditions={"username": "player1"},
            returning_fields=["username", "total_score", "games_played"]
        )
        
        if result['success']:
            print(f"更新成功: 影响了 {result['affected_rows']} 行")
            if result['returned_data']:
                for data in result['returned_data']:
                    print(f"  更新后: {data['username']} - 总分: {data['total_score']}, 游戏数: {data['games_played']}")
        else:
            print(f"更新失败: {result['error']}")
        
        # 3. 测试 update_data - 更新不存在的用户
        print("\n=== 3. 测试 update_data - 更新不存在的用户 ===")
        
        result = await db_manager.update_data(
            table_name="users",
            update_data={"total_score": 2000},
            where_conditions={"username": "nonexistent_user"}
        )
        
        print(f"更新不存在用户: 成功={result['success']}, 影响行数={result['affected_rows']}")
        
        # 4. 测试 insert_or_update - 更新现有用户
        print("\n=== 4. 测试 insert_or_update - 更新现有用户 ===")
        
        result = await db_manager.insert_or_update(
            table_name="users",
            data={"username": "player2", "total_score": 1200, "games_played": 6, "games_won": 4},
            conflict_columns=["username"],
            returning_fields=["username", "total_score", "games_played", "games_won"]
        )
        
        if result['success']:
            print(f"操作: {result['operation']}, 插入: {result['inserted']}, 更新: {result['updated']}")
            if result['returned_data']:
                data = result['returned_data']
                print(f"  结果: {data['username']} - 总分: {data['total_score']}, 游戏数: {data['games_played']}, 胜场: {data['games_won']}")
        
        # 5. 测试 insert_or_update - 插入新用户
        print("\n=== 5. 测试 insert_or_update - 插入新用户 ===")
        
        result = await db_manager.insert_or_update(
            table_name="users",
            data={"username": "player3", "password_hash": "hash3", "total_score": 500, "games_played": 2},
            conflict_columns=["username"],
            returning_fields=["id", "username", "total_score"]
        )
        
        if result['success']:
            print(f"操作: {result['operation']}, 插入: {result['inserted']}, 更新: {result['updated']}")
            if result['inserted']:
                print(f"  新用户ID: {result['last_insert_id']}")
            if result['returned_data']:
                data = result['returned_data']
                print(f"  结果: {data['username']} - ID: {data['id']}, 总分: {data['total_score']}")
        
        # 6. 测试批量更新多个条件
        print("\n=== 6. 测试 update_data - 批量更新 ===")
        
        result = await db_manager.update_data(
            table_name="users",
            update_data={"user_level": 2},
            where_conditions={"total_score": 1200}  # 更新所有总分为1200的用户
        )
        
        print(f"批量更新用户等级: 影响了 {result['affected_rows']} 行")
        
        # 7. 查看最终结果
        print("\n=== 7. 最终用户数据 ===")
        
        users = await db_manager.select_data(
            "users", 
            ["id", "username", "total_score", "games_played", "games_won", "user_level"],
            order_by="total_score DESC"
        )
        
        print("最终用户数据:")
        for user in users['data']:
            print(f"  - ID: {user['id']}, 用户: {user['username']}, 总分: {user['total_score']}, "
                  f"游戏数: {user['games_played']}, 胜场: {user['games_won']}, 等级: {user['user_level']}")
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理测试数据库文件
        if os.path.exists(db_path):
            os.remove(db_path)
            print(f"🗑️ 已清理测试数据库文件: {db_path}")


if __name__ == "__main__":
    print("🚀 开始测试数据库更新接口...")
    asyncio.run(test_update_interfaces())
