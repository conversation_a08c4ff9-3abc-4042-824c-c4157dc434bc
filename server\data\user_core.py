"""
用户业务逻辑核心模块
"""

import asyncio
from typing import Dict, Any, Optional, Callable
import bcrypt


class UserCore:
    """用户业务逻辑核心
    
    负责处理所有用户相关的业务逻辑：
    - 用户注册、登录、登出
    - 用户资料管理
    - 用户排行榜
    - 用户统计信息
    """
    
    def __init__(self, format_data: Callable, db_manager, logger):
        """初始化用户核心模块
        
        Args:
            db_manager: 数据库管理器
            logger: 日志记录器
        """
        self.format_data = format_data
        self.db_manager = db_manager
        self.logger = logger
        
        # 在线用户缓存
        self.online_users = {}  # {user_id: user_info}
        self.user_sessions = {}  # {socket_id: user_id}

    async def cleanup(self):
        """清理资源"""
        self.online_users.clear()
        self.user_sessions.clear()
        self.logger.info("用户核心模块资源清理完成")

    
    # ==================== 消息处理器 ====================
    
    async def handle_user_register(self, data_list: list) -> Optional[Dict[str, Any]]:
        """处理用户注册"""
        try:
            username = data_list[1].get('username', '').strip()
            password = data_list[1].get('password', '')
            invitation = data_list[1].get('invitation', '').strip()

            # 验证邀请码是否有效（从数据库读取）
            if not await self._validate_invitation_code(invitation):
                return self.format_data(
                    'user_register_response',
                    {'type': 'wrong_invitation','message': '邀请码错误'},
                    data_list[2]
                    )

            # 客户端发送SHA256哈希，服务器端直接用bcrypt加盐
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            self.logger.debug(f"创建用户: {username}, SHA256哈希密码bcrypt加盐处理")

            # 使用通用接口插入
            result = await self.db_manager.insert_or_ignore(
                'users', 
                {'username': username,'password_hash': password_hash}, 
                returning_fields=['id', 'username']
            )
            
            if result['success']: # SQL执行成功
                if result['inserted']: # 实际插入了新记录
                    self.logger.info(f"用户创建成功: {username} (ID: {result['last_insert_id']})")
                    response_data = {
                        'type': 'success',
                        'message': '恭喜您注册成功，请返回登录界面进行登录。',
                    }
                else:
                    self.logger.warning(f"用户创建失败: {username} - 用户名已存在")
                    response_data = {
                        'type': 'same_name',
                        'message': '该用户名已被注册，请选择其他用户名。',
                    }
            else:
                self.logger.error(f"用户创建失败: {username} - {result.get('error')}")
                response_data = {
                    'type': 'error',
                    'message': '数据库写入异常，用户创建失败。'
                }
            return self.format_data('user_register_response', response_data, data_list[2])
            
        except Exception as e:
            self.logger.error(f"处理用户注册异常: {e}")
            return self.format_data(
                'user_register_response',
                {'type': 'error', 'message': '服务器内部错误，请稍后重试。'},
                data_list[2]
                )

    
    async def handle_user_login(self, data_list: list) -> Optional[Dict[str, Any]]:
        """处理用户登录"""
        try:
            username = data_list[1].get('username', '').strip()
            password = data_list[1].get('password', '')
            socket_id = data_list[2]

            # 使用基础接口查询用户
            result = await self.db_manager.select_one(
                table_name="users",
                columns=["id", "username", "password_hash", "total_score", "games_played", "games_won", "user_level"],
                where_conditions={"username": username, "is_active": 1}
            )

            if not result['success'] or not result['found']:
                return self.format_data(
                    'user_login_response',
                    {'success': False, 'error': '用户名或密码错误'},
                    socket_id
                    )

            user_data = result['data']
            stored_hash = user_data['password_hash']

            # 验证密码
            if is_password_hashed:
                # 客户端发送SHA256哈希，服务器端使用bcrypt验证SHA256哈希
                password_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                self.logger.debug(f"SHA256哈希密码验证: {username} -> {'成功' if password_valid else '失败'}")
            else:
                # 明文密码，使用bcrypt验证
                password_valid = bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
                self.logger.debug(f"明文密码验证: {username} -> {'成功' if password_valid else '失败'}")

            if password_valid:
                # 使用基础接口更新最后登录时间
                await self.update_data(
                    table_name="users",
                    update_data={"last_login": "datetime('now', 'localtime')"},
                    where_conditions={"id": user_data['id']}
                )

                return {
                    'id': user_data['id'],
                    'username': user_data['username'],
                    'total_score': user_data['total_score'],
                    'games_played': user_data['games_played'],
                    'games_won': user_data['games_won'],
                    'user_level': user_data['user_level']
                }

            if user_data:
                # 登录成功，更新在线用户缓存
                user_id = user_data['id']
                self.online_users[user_id] = {
                    'user_id': user_id,
                    'username': user_data['username'],
                    'socket_id': socket_id,
                    'login_time': asyncio.get_event_loop().time(),
                    'last_activity': asyncio.get_event_loop().time()
                }
                self.user_sessions[socket_id] = user_id
                
                self.logger.info(f"用户登录成功: {username} (ID: {user_id}, Socket: {socket_id})")
                
                return {
                    'type': 'user_login_response',
                    'data': {
                        'success': True,
                        'user_data': {
                            'id': user_data['id'],
                            'username': user_data['username'],
                            'total_score': user_data['total_score'],
                            'games_played': user_data['games_played'],
                            'games_won': user_data['games_won'],
                            'user_level': user_data['user_level']
                        },
                        'message': '登录成功'
                    },
                    'socket_id': socket_id,
                    'user_id': user_data['id'],
                    'user_name': user_data['username'],
                    'broadcast': 0
                }
            
            else:
                self.logger.warning(f"用户登录失败: {username} - 用户名或密码错误")
                return {
                    'type': 'user_login_response',
                    'data': {
                        'success': False,
                        'error': '用户名或密码错误'
                    },
                    'socket_id': socket_id,
                    'user_id': None,
                    'user_name': None,
                    'broadcast': 0
                }
                
        except Exception as e:
            self.logger.error(f"处理用户登录异常: {e}")
            return {
                'type': 'user_login_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id'),
                'user_id': None,
                'user_name': None,
                'broadcast': 0
            }
    
    async def handle_user_logout(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理用户登出"""
        try:
            socket_id = message.get('socket_id')
            user_id = self.user_sessions.get(socket_id)
            
            if user_id:
                user_info = self.online_users.get(user_id)
                username = user_info['username'] if user_info else 'Unknown'
                
                # 清理缓存
                self.online_users.pop(user_id, None)
                self.user_sessions.pop(socket_id, None)
                
                self.logger.info(f"用户登出: {username} (ID: {user_id}, Socket: {socket_id})")
                
                return {
                    'type': 'user_logout_response',
                    'data': {
                        'success': True,
                        'message': '登出成功'
                    },
                    'socket_id': socket_id,
                    'user_id': user_id,
                    'user_name': username,
                    'broadcast': 0
                }
            else:
                return {
                    'type': 'user_logout_response',
                    'data': {
                        'success': False,
                        'error': '用户未登录'
                    },
                    'socket_id': socket_id,
                    'user_id': None,
                    'user_name': None,
                    'broadcast': 0
                }
                
        except Exception as e:
            self.logger.error(f"处理用户登出异常: {e}")
            return {
                'type': 'user_logout_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id'),
                'user_id': None,
                'user_name': None,
                'broadcast': 0
            }
    
    async def handle_get_user_profile(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理获取用户资料"""
        try:
            socket_id = message.get('socket_id')
            user_id = self.user_sessions.get(socket_id)
            
            if not user_id:
                return {
                    'type': 'get_user_profile_response',
                    'data': {
                        'success': False,
                        'error': '用户未登录'
                    },
                    'socket_id': socket_id,
                    'user_id': None,
                    'user_name': None,
                    'room_id': None,
                    'broadcast': 0
                }
            
            # 从缓存获取用户信息
            user_info = self.online_users.get(user_id)
            if user_info:
                return {
                    'type': 'get_user_profile_response',
                    'data': {
                        'success': True,
                        'profile': {
                            'user_id': user_info['user_id'],
                            'username': user_info['username'],
                            'online_time': asyncio.get_event_loop().time() - user_info['login_time']
                        }
                    },
                    'socket_id': socket_id,
                    'user_id': user_info['user_id'],
                    'user_name': user_info['username'],
                    'room_id': None,
                    'broadcast': 0
                }
            else:
                return {
                    'type': 'get_user_profile_response',
                    'data': {
                        'success': False,
                        'error': '用户信息不存在'
                    },
                    'socket_id': socket_id,
                    'user_id': None,
                    'user_name': None,
                    'room_id': None,
                    'broadcast': 0
                }
                
        except Exception as e:
            self.logger.error(f"处理获取用户资料异常: {e}")
            return {
                'type': 'get_user_profile_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id'),
                'user_id': None,
                'user_name': None,
                'room_id': None,
                'broadcast': 0
            }
    
    async def handle_update_user_profile(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理更新用户资料"""
        # TODO: 实现用户资料更新逻辑
        return {
            'type': 'update_user_profile_response',
            'data': {
                'success': False,
                'error': '功能暂未实现'
            },
            'socket_id': message.get('socket_id'),
            'user_id': None,
            'user_name': None,
            'room_id': None,
            'broadcast': 0
        }
    
    async def handle_get_user_ranking(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理获取用户排行榜"""
        try:
            data = message.get('data', {})
            limit = data.get('limit', 10)
            
            # 获取排行榜数据
            ranking = await self.db_manager.get_user_ranking(limit)
            
            return {
                'type': 'get_user_ranking_response',
                'data': {
                    'success': True,
                    'ranking': ranking,
                    'total_online_users': len(self.online_users)
                },
                'socket_id': message.get('socket_id'),
                'user_id': None,  # 排行榜请求不需要特定用户信息
                'user_name': None,
                'room_id': None,
                'broadcast': 0
            }
            
        except Exception as e:
            self.logger.error(f"处理获取排行榜异常: {e}")
            return {
                'type': 'get_user_ranking_response',
                'data': {
                    'success': False,
                    'error': '服务器内部错误'
                },
                'socket_id': message.get('socket_id'),
                'user_id': None,
                'user_name': None,
                'room_id': None,
                'broadcast': 0
            }
    
    # ==================== 辅助方法 ====================
    

    async def _validate_invitation_code(self, invitation: str) -> bool:
        """验证邀请码是否有效 - 从数据库读取"""
        try:
            # 从数据库读取当前有效的邀请码
            valid_code = await self.db_manager.get_system_config('invitation_code')

            if not valid_code:
                self.logger.warning("数据库中未找到邀请码配置，使用默认值")
                valid_code = 'Welcome24inWar'  # 默认邀请码

            # 不区分大小写比较
            return invitation.strip().lower() == valid_code.lower()

        except Exception as e:
            self.logger.error(f"验证邀请码时发生异常: {e}")
            # 异常情况下使用默认邀请码验证
            return invitation.strip().lower() == 'welcome24inwar'
    
    def get_online_user_count(self) -> int:
        """获取在线用户数量"""
        return len(self.online_users)
    
    def get_user_by_socket(self, socket_id: str) -> Optional[Dict[str, Any]]:
        """根据socket_id获取用户信息"""
        user_id = self.user_sessions.get(socket_id)
        return self.online_users.get(user_id) if user_id else None
    
    def disconnect_user(self, socket_id: str):
        """断开用户连接（清理缓存）"""
        user_id = self.user_sessions.get(socket_id)
        if user_id:
            user_info = self.online_users.get(user_id)
            username = user_info['username'] if user_info else 'Unknown'
            
            self.online_users.pop(user_id, None)
            self.user_sessions.pop(socket_id, None)
            
            self.logger.info(f"用户连接断开: {username} (ID: {user_id}, Socket: {socket_id})")
