from aiohttp import web
import aiohttp
import asyncio
import json
from typing import Dict, Any, Set, Optional


class ConnectionManager:
    def __init__(self, logger, network_receive_queue, network_send_queue):
        self.logger = logger
        self.active_connections: Dict[str, web.WebSocketResponse] = {}
        self.active_connections_lock = asyncio.Lock()
        self.room_members: Dict[str, Dict[str, str]] = {}
        self.room_members_lock = asyncio.Lock()
        self.network_receive_queue = network_receive_queue  # 网络接收队列
        self.network_send_queue = network_send_queue  # 网络发送队列
        self.is_shutting_down = False

    async def handle_new_websocket(self, request):
        """处理新的WebSocket连接请求"""
        if self.is_shutting_down:
            self.logger.info("服务器正在关闭")
            return web.Response(status=503, text="服务器正在关闭")
            
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        socket_id = await self.connect(ws)
        
        try:
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    await self.handle_receive_message(msg.data, socket_id)
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    self.logger.error(f"WebSocket连接错误 (ID: {socket_id}): {ws.exception()}")
        finally:
            self.logger.info(f"{socket_id})的WebSocket连接关闭")
            await self.disconnect(socket_id)
        
        return ws


    async def handle_receive_message(self, message: str, socket_id: str):   # 网络消息包格式规范：{'type': str, 'data': dict}
        """处理接收到的消息

        安全机制：
        - 屏蔽admin_command和admin_response消息类型
        - admin命令只能通过管理员控制台发送
        - 防止客户端绕过认证机制
        """
        try:
            json_message: Dict[str, Any] = json.loads(message)

            # 完整的消息格式验证
            if not self._validate_complete_message_format(json_message):
                await self._send_error_response(socket_id, "消息格式错误")
                return

            # 补全七字段格式
            json_message = self._ensure_seven_fields(json_message, socket_id)

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败 (ID: {socket_id}): {e}")
            await self._send_error_response(socket_id, "无效的JSON格式")
            return
        except Exception as e:
            self.logger.error(f"消息解析异常 (ID: {socket_id}): {e}")
            await self._send_error_response(socket_id, "服务器内部错误")
            return

        
        '''网络层的初步消息路由'''
        message_type = json_message.get('type','')
        try:
            # 安全检查：屏蔽admin相关消息类型
            if message_type in ['admin_command', 'admin_response']:
                self.logger.warning(f'客户端 {socket_id} 尝试发送被禁止的admin消息类型: {message_type}')
                return
            # 处理聊天消息（直接广播，不进入消息总线）
            if message_type == 'chat':
                await self.send_message(json_message)
                self.logger.debug(f"Chat消息已直接广播，不进入消息总线: {socket_id}")
            # 处理心跳消息（直接在网络层处理，不进入消息总线）
            elif message_type == 'heartbeat':
                self.logger.debug(f"Heartbeat消息已在网络层处理，不进入消息总线: {socket_id}")
                # 心跳包只需要更新连接的最后活跃时间，不需要响应
                # 连接字典的维护和掉线检测将在后续实现
                pass
            else:
                # 其他消息类型（如test）放入网络接收队列，进入消息总线
                if self.network_receive_queue:
                    # 消息已经过完整验证和七字段补全，直接放入队列
                    await self.network_receive_queue.put(json_message)
                    self.logger.timing("NET_RECV", socket_id, message_type)  # 性能日志
                    self.logger.info(f'📥 [Network] {message_type}消息已放入network_receive_queue (ID: {socket_id})')
                else:
                    self.logger.error(f'❌ [Network] network_receive_queue为None!')
        except Exception as e:
            self.logger.error(f'消息处理业务逻辑异常 (ID: {socket_id}): {e}')
            await self._send_error_response(socket_id, "消息处理失败")


    async def connect(self, websocket: web.WebSocketResponse) -> str:
        """添加新的WebSocket连接并返回连接ID（使用对象地址）"""
        socket_id = str(id(websocket))  # 使用WebSocket对象的内存地址
        async with self.active_connections_lock:
            self.active_connections[socket_id] = websocket
            self.logger.info(f"新的连接加入 (地址: {socket_id})，当前活动连接数：{len(self.active_connections)}")
        return socket_id
    

    async def disconnect(self, socket_id: str):
        """移除WebSocket连接"""
        async with self.active_connections_lock:
            if socket_id in self.active_connections:
                if not self.active_connections[socket_id].closed:
                    await self.active_connections[socket_id].close()
                del self.active_connections[socket_id]            
            self.logger.info(f"连接断开 (ID: {socket_id})，当前活动连接数：{len(self.active_connections)}")
 

    async def send_message(self, message: Dict[str, Any]):
        # 参数验证
        if not isinstance(message, dict):
            self.logger.error(f"发送消息格式错误: {type(message)}")
            return

        # 消息预处理，主要过滤并处理一些服务器内部消息
        if message.get('type') == 'room_sync':
            self.logger.info(f"网络层收到房间同步消息，正在同步房间成员字典...")
            async with self.room_members_lock:
                self.room_members = message.get('data', {})
            return
        
        broadcast = message.get('broadcast', 0)
        socket_id = message.get('socket_id', '')
        room_id = message.get('room_id', '')

        try:
        # 快速获取连接副本，减少锁持有时间
            async with self.active_connections_lock:
                connections_copy = dict(self.active_connections)
        except MemoryError:
            self.logger.error("内存不足，无法复制连接字典")
            return
        except Exception as e:
            self.logger.error(f"复制连接字典失败: {e}")
            return

        # 并发发送到所有连接的方法
        async def async_send_message(soc_id, conn: web.WebSocketResponse|None):
            try:
                if conn is None:  # 添加None检查
                    return soc_id, False, "连接对象为None"
                await conn.send_json(message)
                return soc_id, True, None
            except Exception as e:
                return soc_id, False, str(e)
        
        tasks = []
        try:
            # 向所有连接的客户端广播消息
            if broadcast == 1:
                # 创建并发任务
                tasks = [
                    async_send_message(soc_id, conn)
                    for soc_id, conn in connections_copy.items()
                ]
                self.logger.info(f"正在向所有 {len(tasks)} 个连接广播消息")

            # 向指定房间广播消息
            elif broadcast == 2:
                if not room_id:
                    self.logger.error(f"❌ [Network] 房间广播但未提供目标room_id")
                    return
                async with self.room_members_lock:
                    room_members_copy = list(self.room_members.get(room_id, {}).keys())
                tasks = [
                    async_send_message(soc_id, conn)
                    for soc_id in room_members_copy
                    if (conn := connections_copy.get(soc_id)) is not None
                ]
                room_members_copy.clear()
                self.logger.info(f"正在向房间的 {len(tasks)} 个成员广播消息")

            # 向指定连接发送消息
            elif broadcast == 0:
                connection = connections_copy.get(socket_id)
                if not connection:
                    self.logger.error(f"❌ [Network] 单发消息但未找到目标连接 (ID: {socket_id})")
                    return
                tasks = [async_send_message(socket_id, connection)]
                self.logger.info(f"正在向单个连接发送消息 (ID: {socket_id})")
            else:
                self.logger.error(f"❌ [Network] 不支持的广播类型: {broadcast}")
                return
        except Exception as e:
            self.logger.error(f"❌ [Network] 创建发送任务失败: {e}")
            return
        finally:
            #断开浅拷贝的连接字典，避免阻止有些连接对象的清理
            connections_copy.clear()

        # 处理空任务列表的情况
        if not tasks:
            self.logger.info("没有活跃连接，跳过广播")
            return

        # 并发执行所有发送任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = 0
        for result in results:
            if isinstance(result, tuple):
                socket_id, success, error = result
                if success:
                    success_count += 1
                else:
                    self.logger.error(f"发送消息时出错 (ID: {socket_id}): {error}")
            else:
                self.logger.error(f"广播任务异常: {result}")

        self.logger.info(f"广播完成: {success_count}/{len(tasks)} 成功")
    
    async def shutdown(self):
        """关闭所有连接并停止服务器"""
        self.is_shutting_down = True
        self.logger.info("开始关闭服务器...")
        
        async with self.active_connections_lock:
            # 关闭所有连接
            for socket_id, ws in self.active_connections.items():
                try:
                    await ws.close(code=1000, message=b"Server shutting down")
                    self.logger.info(f"关闭连接 (ID: {socket_id})")
                except Exception as e:
                    self.logger.error(f"关闭连接时出错 (ID: {socket_id}): {str(e)}")
            
            # 清空连接列表
            self.active_connections.clear()
        
        self.logger.info("所有连接已关闭")

    async def start_server(self, host='127.0.0.1', port=8080):
        """启动网络服务器（在网络线程中调用）

        包含：
        1. 创建web应用
        2. 注册WebSocket路由
        3. 启动网络发送队列处理任务
        4. 启动HTTP服务器

        Args:
            host: 服务器主机地址
            port: 服务器端口
        """
        try:
            # 1. 创建 web 应用
            self.logger.info("创建 Web 应用")
            app = web.Application()

            # 2. 注册路由
            self.logger.info("注册 WebSocket 路由")
            app.router.add_get('/ws', self.handle_new_websocket)

            # 3. 启动网络发送队列处理任务并等待其完全启动
            self.logger.info("启动网络发送队列处理任务")
            send_loop_ready = asyncio.Event()
            asyncio.create_task(self._network_send_loop(send_loop_ready))

            # 等待发送循环完全启动
            self.logger.info("等待网络发送队列处理循环启动完成...")
            await send_loop_ready.wait()
            self.logger.info("网络发送队列处理循环已就绪")

            # 4. 启动服务器
            self.logger.info(f"启动 Web 服务器 ({host}:{port})")
            await web._run_app(app, host=host, port=port)

        except Exception as e:
            self.logger.error(f"网络服务器启动失败: {e}")
            raise

    async def _network_send_loop(self, ready_event: Optional[asyncio.Event] = None):
        """网络发送队列处理循环（在网络线程中运行）

        Args:
            ready_event: 可选的事件对象，用于通知循环已启动
        """
        self.logger.info("网络发送队列处理循环启动")

        # 通知调用者循环已启动
        if ready_event:
            ready_event.set()
            self.logger.debug("网络发送队列处理循环就绪信号已发送")

        while not self.is_shutting_down:
            try:
                # 使用超时机制优化响应性能
                get_task = asyncio.create_task(self.network_send_queue.get())
                sleep_task = asyncio.create_task(asyncio.sleep(0.02))  # 优化：从100ms降低到20ms

                try:
                    done, _ = await asyncio.wait(
                        [get_task, sleep_task],
                        return_when=asyncio.FIRST_COMPLETED
                    )

                    if get_task in done:
                        # 有消息到达，获取并处理
                        response_message = await get_task
                        sleep_task.cancel()  # 取消睡眠任务

                        socket_id = response_message.get('socket_id')
                        message_type = response_message.get('type')
                        self.logger.timing("NET_SEND", socket_id, message_type)  # 性能日志
                        
                        await self.send_message(response_message)
                        self.logger.info(f"✅ [Network] {message_type}消息已从队列移交发送程序")

                    else:
                        # 超时，取消获取任务，继续循环检查is_shutting_down状态
                        get_task.cancel()
                        continue  # 跳过消息处理，直接检查关闭状态

                except asyncio.CancelledError:
                    # 清理未完成的任务
                    if not get_task.done():
                        get_task.cancel()
                    if not sleep_task.done():
                        sleep_task.cancel()
                    break

            except Exception as e:
                self.logger.error(f"❌ [Network] 网络发送队列处理错误: {e}")
                import traceback
                self.logger.error(f"❌ [Network] 发送异常堆栈: {traceback.format_exc()}")
                # 继续处理下一个消息，不中断循环

        self.logger.info("网络发送队列处理循环停止")

    def _validate_complete_message_format(self, message: Dict[str, Any]) -> bool:
        """完整的消息格式验证

        验证七字段格式的必需字段和数据类型
        """
        try:
            # 1. 基本类型检查
            if not isinstance(message, dict):
                self.logger.error(f"消息不是字典类型: {type(message)}")
                return False

            # 2. 必需字段检查
            if 'type' not in message:
                self.logger.error("消息缺少type字段")
                return False

            if 'data' not in message:
                self.logger.error("消息缺少data字段")
                return False

            # 3. 字段类型检查
            if not isinstance(message['data'], dict):
                self.logger.error(f"data字段必须是字典类型: {type(message['data'])}")
                return False

            if not isinstance(message['type'], str):
                self.logger.error(f"type字段必须是字符串类型: {type(message['type'])}")
                return False

            # 4. type字段不能为空
            if not message['type'].strip():
                self.logger.error("type字段不能为空")
                return False

            return True

        except Exception as e:
            self.logger.error(f"消息格式验证异常: {e}")
            return False

    def _ensure_seven_fields(self, message: Dict[str, Any], socket_id: str) -> Dict[str, Any]:
        """确保消息包含完整的七字段格式

        为缺失的字段添加默认值
        """
        # 确保socket_id字段
        message['socket_id'] = socket_id

        # 确保其他字段存在，如果不存在则添加默认值
        if 'user_id' not in message:
            message['user_id'] = None
        if 'user_name' not in message:
            message['user_name'] = None
        if 'broadcast' not in message:
            message['broadcast'] = 0

        return message

    async def _send_error_response(self, socket_id: str, error_message: str):
        """发送错误响应给客户端"""
        try:
            error_response = {
                'type': 'error',
                'data': {'message': error_message},
                'socket_id': socket_id,
                'user_id': None,
                'user_name': None,
                'room_id': None,
                'broadcast': 0
            }

            # 直接发送，不通过消息队列
            if socket_id in self.active_connections:
                connection = self.active_connections[socket_id]
                if connection and not connection.closed:
                    await connection.send_json(error_response)
                    self.logger.info(f"已发送错误响应给客户端 (ID: {socket_id}): {error_message}")
                else:
                    self.logger.warning(f"连接已关闭，无法发送错误响应 (ID: {socket_id})")
            else:
                self.logger.warning(f"连接不存在，无法发送错误响应 (ID: {socket_id})")

        except Exception as e:
            self.logger.error(f"发送错误响应失败 (ID: {socket_id}): {e}")


