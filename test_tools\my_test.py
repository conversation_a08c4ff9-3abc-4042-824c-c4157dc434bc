

active_connections = {
    'hall_01': {
        'room_01': {
            'socket_001': 1,  # WebSocketResponse对象
            'socket_002': 2   # WebSocketResponse对象
        },
        'room_02': {
            'socket_003': 3   # WebSocketResponse对象
        },
        'lobby': {  # 大厅中未进房间的用户
            'socket_004': 4   # WebSocketResponse对象
        }
    },
    'hall_02': {
        'room_01': {
            'socket_005': 5   # WebSocketResponse对象
        }
    }
}
def test(a):
    return a
socket = []
connections = []
socket = [ test(socket_id)
    for room_dict in active_connections.values()
    for socket_dict in room_dict.values()
    for socket_id in socket_dict.keys()
]
for i in active_connections.values():
    print(i)

all = {1:1, 2:2, 3:3, 4:4, 5:5, 6:6}
lis = [1, 3, 4]

b = ''

def test1():
    
    return (a := 5)
if b:
    print('b')
